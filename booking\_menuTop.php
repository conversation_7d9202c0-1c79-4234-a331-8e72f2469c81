<!--  Header Start -->
<style>
.online-users-display {
    font-size: 0.875rem;
}

.online-users-display .dropdown-menu {
    min-width: 280px;
    max-height: 400px;
    overflow-y: auto;
}

.online-users-display .dropdown-item {
    border: none;
    padding: 0.5rem 1rem;
}

.online-users-display .dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.online-users-display .badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.online-users-display .dropdown-toggle::after {
    display: none;
}
</style>
<header class="topbar">
    <div class="with-vertical">

        <!-- ---------------------------------- -->
        <!-- Start Vertical Layout Header -->
        <!-- ---------------------------------- -->
        <nav class="navbar navbar-expand-lg p-0">
            <ul class="navbar-nav">
                <li class="nav-item d-flex d-xl-none">
                    <a class="nav-link nav-icon-hover-bg rounded-circle  sidebartoggler " id="headerCollapse"
                        href="javascript:void(0)">
                        <iconify-icon icon="solar:hamburger-menu-line-duotone" class="fs-6"></iconify-icon>
                    </a>
                </li>
                <!--
                        <li class="nav-item d-none d-xl-flex nav-icon-hover-bg rounded-circle">
                            <a class="nav-link" href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#exampleModal">
                                <iconify-icon icon="solar:magnifer-linear" class="fs-6"></iconify-icon>
                            </a>
                        </li> 
                        <li class="nav-item d-none d-lg-flex dropdown nav-icon-hover-bg rounded-circle">
                            <div class="hover-dd">
                                <a class="nav-link" id="drop2" href="javascript:void(0)" aria-haspopup="true" aria-expanded="false">
                                    <iconify-icon icon="solar:widget-3-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                                <div class="dropdown-menu dropdown-menu-nav dropdown-menu-animate-up py-0 overflow-hidden" aria-labelledby="drop2">
                                    <div class="position-relative">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="p-4 pb-3">

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="position-relative">
                                                                <a href="../main/app-chat.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-primary-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon
                                                                            icon="solar:chat-line-bold-duotone"
                                                                            class="fs-7 text-primary"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">Chat Application</h6>
                                                                        <span class="fs-11 d-block text-body-color">New
                                                                            messages arrived</span>
                                                                    </div>
                                                                </a>
                                                                <a href="../main/app-invoice.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-secondary-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon
                                                                            icon="solar:bill-list-bold-duotone"
                                                                            class="fs-7 text-secondary"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">Invoice App</h6>
                                                                        <span class="fs-11 d-block text-body-color">Get
                                                                            latest invoice</span>
                                                                    </div>
                                                                </a>
                                                                <a href="../main/app-contact2.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-warning-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon
                                                                            icon="solar:phone-calling-rounded-bold-duotone"
                                                                            class="fs-7 text-warning"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">Contact Application</h6>
                                                                        <span class="fs-11 d-block text-body-color">2
                                                                            Unsaved Contacts</span>
                                                                    </div>
                                                                </a>
                                                                <a href="../main/app-email.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-danger-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon icon="solar:letter-bold-duotone"
                                                                            class="fs-7 text-danger"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">Email App</h6>
                                                                        <span class="fs-11 d-block text-body-color">Get
                                                                            new emails</span>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="position-relative">
                                                                <a href="../main/page-user-profile.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-success-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon icon="solar:user-bold-duotone"
                                                                            class="fs-7 text-success"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">User Profile</h6>
                                                                        <span
                                                                            class="fs-11 d-block text-body-color">learn
                                                                            more information</span>
                                                                    </div>
                                                                </a>
                                                                <a href="../main/app-calendar.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-primary-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon
                                                                            icon="solar:calendar-minimalistic-bold-duotone"
                                                                            class="fs-7 text-primary"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">Calendar App</h6>
                                                                        <span class="fs-11 d-block text-body-color">Get
                                                                            dates</span>
                                                                    </div>
                                                                </a>
                                                                <a href="../main/app-contact.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-secondary-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon
                                                                            icon="solar:smartphone-2-bold-duotone"
                                                                            class="fs-7 text-secondary"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">Contact List Table</h6>
                                                                        <span class="fs-11 d-block text-body-color">Add
                                                                            new contact</span>
                                                                    </div>
                                                                </a>
                                                                <a href="../main/app-notes.html"
                                                                    class="d-flex align-items-center pb-9 position-relative">
                                                                    <div
                                                                        class="bg-warning-subtle rounded round-48 me-3 d-flex align-items-center justify-content-center">
                                                                        <iconify-icon icon="solar:notes-bold-duotone"
                                                                            class="fs-7 text-warning"></iconify-icon>
                                                                    </div>
                                                                    <div>
                                                                        <h6 class="mb-0">Notes Application</h6>
                                                                        <span
                                                                            class="fs-11 d-block text-body-color">To-do
                                                                            and Daily tasks</span>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-4 d-none d-lg-flex">
                                                <img src="../assets/images/backgrounds/mega-dd-bg.jpg" alt="mega-dd"
                                                    class="img-fluid mega-dd-bg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        -->
            </ul>

            <div class="d-block d-lg-none py-9 py-xl-0">
                <img src="../assets/images/logos/logo.svg" alt="matdash-img" />
            </div>
            <!-- <a class="navbar-toggler p-0 border-0 nav-icon-hover-bg rounded-circle" href="javascript:void(0)" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <iconify-icon icon="solar:menu-dots-bold-duotone" class="fs-6"></iconify-icon>
                    </a> -->
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <div class="d-flex align-items-center justify-content-between">
                    <!-- Online Users Display -->
                    <div class="online-users-display me-3">
                        <div class="d-flex align-items-center">
                            <iconify-icon icon="solar:users-group-rounded-bold-duotone" class="fs-5 text-success me-2"></iconify-icon>
                            <span class="text-muted small">Online: </span>
                            <span id="online-users-count" class="badge bg-success-subtle text-success ms-1">0</span>
                            <div class="dropdown ms-2">
                                <button class="btn btn-sm btn-outline-success dropdown-toggle" type="button" id="onlineUsersDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <iconify-icon icon="solar:eye-bold-duotone" class="fs-6"></iconify-icon>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="onlineUsersDropdown" id="online-users-list">
                                    <li><span class="dropdown-item-text text-muted">Loading...</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <ul class="navbar-nav flex-row mx-auto ms-lg-auto align-items-center justify-content-center">
                        <!-- <li class="nav-item dropdown">
                                    <a href="javascript:void(0)"
                                        class="nav-link nav-icon-hover-bg rounded-circle d-flex d-lg-none align-items-center justify-content-center"
                                        type="button" data-bs-toggle="offcanvas" data-bs-target="#mobilenavbar" aria-controls="offcanvasWithBothOptions">
                                        <iconify-icon icon="solar:sort-line-duotone" class="fs-6"></iconify-icon>
                                    </a>
                                </li> -->
                        <!-- <li class="nav-item">
                                    <a class="nav-link moon dark-layout nav-icon-hover-bg rounded-circle"
                                        href="javascript:void(0)">
                                        <iconify-icon icon="solar:moon-line-duotone" class="moon fs-6"></iconify-icon>
                                    </a>
                                    <a class="nav-link sun light-layout nav-icon-hover-bg rounded-circle"
                                        href="javascript:void(0)" style="display: none">
                                        <iconify-icon icon="solar:sun-2-line-duotone" class="sun fs-6"></iconify-icon>
                                    </a>
                                </li> -->
                        <!-- <li class="nav-item d-block d-xl-none">
                                    <a class="nav-link nav-icon-hover-bg rounded-circle" href="javascript:void(0)"
                                        data-bs-toggle="modal" data-bs-target="#exampleModal">
                                        <iconify-icon icon="solar:magnifer-line-duotone" class="fs-6"></iconify-icon>
                                    </a>
                                </li> -->

                        <!-- ------------------------------- -->
                        <!-- start notification Dropdown -->
                        <!-- ------------------------------- -->
                        <!-- <li class="nav-item dropdown nav-icon-hover-bg rounded-circle">
                                    <a class="nav-link position-relative" href="javascript:void(0)" id="drop2"
                                        aria-expanded="false">
                                        <iconify-icon icon="solar:bell-bing-line-duotone" class="fs-6"></iconify-icon>
                                    </a>
                                    <div class="dropdown-menu content-dd dropdown-menu-end dropdown-menu-animate-up"
                                        aria-labelledby="drop2">
                                        <div class="d-flex align-items-center justify-content-between py-3 px-7">
                                            <h5 class="mb-0 fs-5 fw-semibold">Notifications</h5>
                                            <span class="badge text-bg-primary rounded-4 px-3 py-1 lh-sm">5 new</span>
                                        </div>
                                        <div class="message-body" data-simplebar>
                                            <a href="javascript:void(0)"
                                                class="py-6 px-7 d-flex align-items-center dropdown-item gap-3">
                                                <span
                                                    class="flex-shrink-0 bg-danger-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-danger">
                                                    <iconify-icon icon="solar:widget-3-line-duotone"></iconify-icon>
                                                </span>
                                                <div class="w-75">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <h6 class="mb-1 fw-semibold">Launch Admin</h6>
                                                        <span class="d-block fs-2">9:30 AM</span>
                                                    </div>
                                                    <span class="d-block text-truncate text-truncate fs-11">Just see the
                                                        my new admin!</span>
                                                </div>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="py-6 px-7 d-flex align-items-center dropdown-item gap-3">
                                                <span
                                                    class="flex-shrink-0 bg-primary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-primary">
                                                    <iconify-icon icon="solar:calendar-line-duotone"></iconify-icon>
                                                </span>
                                                <div class="w-75">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <h6 class="mb-1 fw-semibold">Event today</h6>
                                                        <span class="d-block fs-2">9:15 AM</span>
                                                    </div>
                                                    <span class="d-block text-truncate text-truncate fs-11">Just a
                                                        reminder that you have event</span>
                                                </div>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="py-6 px-7 d-flex align-items-center dropdown-item gap-3">
                                                <span
                                                    class="flex-shrink-0 bg-secondary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-secondary">
                                                    <iconify-icon icon="solar:settings-line-duotone"></iconify-icon>
                                                </span>
                                                <div class="w-75">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <h6 class="mb-1 fw-semibold">Settings</h6>
                                                        <span class="d-block fs-2">4:36 PM</span>
                                                    </div>
                                                    <span class="d-block text-truncate text-truncate fs-11">You can
                                                        customize this template as you want</span>
                                                </div>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="py-6 px-7 d-flex align-items-center dropdown-item gap-3">
                                                <span
                                                    class="flex-shrink-0 bg-warning-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-warning">
                                                    <iconify-icon icon="solar:widget-4-line-duotone"></iconify-icon>
                                                </span>
                                                <div class="w-75">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <h6 class="mb-1 fw-semibold">Launch Admin</h6>
                                                        <span class="d-block fs-2">9:30 AM</span>
                                                    </div>
                                                    <span class="d-block text-truncate text-truncate fs-11">Just see the
                                                        my new admin!</span>
                                                </div>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="py-6 px-7 d-flex align-items-center dropdown-item gap-3">
                                                <span
                                                    class="flex-shrink-0 bg-primary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-primary">
                                                    <iconify-icon icon="solar:calendar-line-duotone"></iconify-icon>
                                                </span>
                                                <div class="w-75">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <h6 class="mb-1 fw-semibold">Event today</h6>
                                                        <span class="d-block fs-2">9:15 AM</span>
                                                    </div>
                                                    <span class="d-block text-truncate text-truncate fs-11">Just a
                                                        reminder that you have event</span>
                                                </div>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="py-6 px-7 d-flex align-items-center dropdown-item gap-3">
                                                <span
                                                    class="flex-shrink-0 bg-secondary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-secondary">
                                                    <iconify-icon icon="solar:settings-line-duotone"></iconify-icon>
                                                </span>
                                                <div class="w-75">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <h6 class="mb-1 fw-semibold">Settings</h6>
                                                        <span class="d-block fs-2">4:36 PM</span>
                                                    </div>
                                                    <span class="d-block text-truncate text-truncate fs-11">You can
                                                        customize this template as you want</span>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="py-6 px-7 mb-1">
                                            <button class="btn btn-primary w-100">See All Notifications</button>
                                        </div>

                                    </div>
                                </li> -->
                        <!-- ------------------------------- -->
                        <!-- end notification Dropdown -->
                        <!-- ------------------------------- -->

                        <!-- ------------------------------- -->
                        <!-- start language Dropdown -->
                        <!-- ------------------------------- -->
                        <!-- <li class="nav-item dropdown nav-icon-hover-bg rounded-circle">
                                    <a class="nav-link" href="javascript:void(0)" id="drop2" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <img src="../assets/images/flag/icon-flag-en.svg" alt="matdash-img" width="20px"
                                            height="20px" class="rounded-circle object-fit-cover round-20" />
                                    </a>
                                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up"
                                        aria-labelledby="drop2">
                                        <div class="message-body">
                                            <a href="javascript:void(0)"
                                                class="d-flex align-items-center gap-2 py-3 px-4 dropdown-item">
                                                <div class="position-relative">
                                                    <img src="../assets/images/flag/icon-flag-en.svg" alt="matdash-img"
                                                        width="20px" height="20px"
                                                        class="rounded-circle object-fit-cover round-20" />
                                                </div>
                                                <p class="mb-0 fs-3">English (UK)</p>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="d-flex align-items-center gap-2 py-3 px-4 dropdown-item">
                                                <div class="position-relative">
                                                    <img src="../assets/images/flag/icon-flag-cn.svg" alt="matdash-img"
                                                        width="20px" height="20px"
                                                        class="rounded-circle object-fit-cover round-20" />
                                                </div>
                                                <p class="mb-0 fs-3">中国人 (Chinese)</p>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="d-flex align-items-center gap-2 py-3 px-4 dropdown-item">
                                                <div class="position-relative">
                                                    <img src="../assets/images/flag/icon-flag-fr.svg" alt="matdash-img"
                                                        width="20px" height="20px"
                                                        class="rounded-circle object-fit-cover round-20" />
                                                </div>
                                                <p class="mb-0 fs-3">français (French)</p>
                                            </a>
                                            <a href="javascript:void(0)"
                                                class="d-flex align-items-center gap-2 py-3 px-4 dropdown-item">
                                                <div class="position-relative">
                                                    <img src="../assets/images/flag/icon-flag-sa.svg" alt="matdash-img"
                                                        width="20px" height="20px"
                                                        class="rounded-circle object-fit-cover round-20" />
                                                </div>
                                                <p class="mb-0 fs-3">عربي (Arabic)</p>
                                            </a>
                                        </div>
                                    </div>
                                </li> -->
                        <!-- ------------------------------- -->
                        <!-- end language Dropdown -->
                        <!-- ------------------------------- -->

                        <!-- ------------------------------- -->
                        <!-- start profile Dropdown -->
                        <!-- ------------------------------- -->
                        <li class="nav-item dropdown">
                            <a class="nav-link" href="javascript:void(0)" id="drop1" aria-expanded="false">
                                <div class="d-flex align-items-center gap-2 lh-base">
                                    <img src="../assets/images/profile/user-1.jpg" class="rounded-circle" width="35"
                                        height="35" alt="matdash-img" />
                                    <iconify-icon icon="solar:alt-arrow-down-bold" class="fs-2"></iconify-icon>
                                </div>
                            </a>
                            <div class="dropdown-menu profile-dropdown dropdown-menu-end dropdown-menu-animate-up"
                                aria-labelledby="drop1">
                                <div class="position-relative px-4 pt-3 pb-2">
                                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom gap-6">
                                        <img src="../assets/images/profile/user-1.jpg" class="rounded-circle" width="56"
                                            height="56" alt="matdash-img" />
                                        <div>
                                            <h5 class="mb-0 fs-12"><?php echo $_SESSION['name']; ?>
                                                <span class="text-success fs-11"><?php echo $_SESSION["userRole"];?></span>
                                            </h5>
                                            <!-- <p class="mb-0 text-dark">
                                                <EMAIL>
                                            </p> -->
                                        </div>
                                    </div>
                                    <div class="message-body">
                                        <!-- <a href="javascript:void(0)" class="p-2 dropdown-item h6 rounded-1">
                                                    My Profile
                                                </a>
                                                <a href="javascript:void(0)" class="p-2 dropdown-item h6 rounded-1">
                                                    My Subscription
                                                </a>
                                                <a href="javascript:void(0)" class="p-2 dropdown-item h6 rounded-1">
                                                    My Statements <span
                                                        class="badge bg-danger-subtle text-danger rounded ms-8">4</span>
                                                </a>
                                                <a href="javascript:void(0)" class="p-2 dropdown-item h6 rounded-1">
                                                    Account Settings
                                                </a> -->
                                        <a href="../signout" class="p-2 dropdown-item h6 rounded-1">
                                            Sign Out
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <!-- ------------------------------- -->
                        <!-- end profile Dropdown -->
                        <!-- ------------------------------- -->
                    </ul>
                </div>
            </div>
        </nav>
        <!-- ---------------------------------- -->
        <!-- End Vertical Layout Header -->
        <!-- ---------------------------------- -->

    </div>

</header>
<!--  Header End -->

<script>
// Online Users Management
let onlineUsersInterval;

function updateOnlineUsers() {
    fetch('../floorplan/get_online_users.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const count = data.users.length;
                const countElement = document.getElementById('online-users-count');
                const listElement = document.getElementById('online-users-list');

                // Update count
                countElement.textContent = count;

                // Update dropdown list
                if (count === 0) {
                    listElement.innerHTML = '<li><span class="dropdown-item-text text-muted">No users online</span></li>';
                } else {
                    let listHTML = '';
                    data.users.forEach(user => {
                        const roleColor = getRoleColor(user.role);
                        const timeAgo = getTimeAgo(user.last_activity);
                        listHTML += `
                            <li>
                                <div class="dropdown-item d-flex align-items-center py-2">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-${roleColor}-subtle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                            <iconify-icon icon="solar:user-bold-duotone" class="fs-6 text-${roleColor}"></iconify-icon>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <div class="fw-semibold text-dark">${user.name}</div>
                                        <div class="small text-muted">${user.role} • ${timeAgo}</div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="badge bg-success rounded-pill" style="width: 8px; height: 8px;"></span>
                                    </div>
                                </div>
                            </li>
                        `;
                    });
                    listElement.innerHTML = listHTML;
                }
            }
        })
        .catch(error => {
            console.error('Error fetching online users:', error);
            document.getElementById('online-users-count').textContent = '?';
            document.getElementById('online-users-list').innerHTML = '<li><span class="dropdown-item-text text-danger">Error loading users</span></li>';
        });
}

function getRoleColor(role) {
    switch(role) {
        case 'super_admin': return 'danger';
        case 'admin': return 'warning';
        case 'user': return 'primary';
        default: return 'secondary';
    }
}

function getTimeAgo(timestamp) {
    const now = new Date();
    const activityTime = new Date(timestamp * 1000);
    const diffInSeconds = Math.floor((now - activityTime) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + 'm ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + 'h ago';
    return Math.floor(diffInSeconds / 86400) + 'd ago';
}

// Initialize online users tracking
document.addEventListener('DOMContentLoaded', function() {
    // Initial load
    updateOnlineUsers();

    // Update every 30 seconds
    onlineUsersInterval = setInterval(updateOnlineUsers, 30000);

    // Update user's last activity every 5 minutes
    setInterval(function() {
        fetch('../floorplan/update_user_activity.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
    }, 300000); // 5 minutes
});

// Clean up interval when page unloads
window.addEventListener('beforeunload', function() {
    if (onlineUsersInterval) {
        clearInterval(onlineUsersInterval);
    }
});
</script>