<?php
/**
 * Booking Model
 * 
 * Handles booking operations
 */
require_once dirname(__DIR__) . '/models/Database.php';

class Booking {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get bookings by date
     * 
     * @param string $date Date in Y-m-d format
     * @param int $zoneId Optional zone ID
     * @return array Bookings data
     */
    public function getBookingsByDate($date, $zoneId = null) {
        try {
            $params = [$date];
            $sql = "SELECT * FROM kp_booking WHERE use_date = ?";
            
            if ($zoneId !== null) {
                $sql .= " AND zone_id = ?";
                $params[] = $zoneId;
            }
            
            $sql .= " ORDER BY create_date DESC";
            
            return $this->db->getRows($sql, $params);
        } catch (Exception $e) {
            error_log("Failed to get bookings by date: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get booking by ID
     * 
     * @param int $bookingId Booking ID
     * @return array|bool Booking data if found, false otherwise
     */
    public function getBookingById($bookingId) {
        try {
            $sql = "SELECT * FROM kp_booking WHERE booking_id = ?";
            return $this->db->getRow($sql, [$bookingId]);
        } catch (Exception $e) {
            error_log("Failed to get booking by ID: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get booking by order number
     *
     * @param string $orderNo Order number
     * @return array|bool Booking data if found, false otherwise
     */
    public function getBookingByOrderNo($orderNo) {
        try {
            $sql = "SELECT * FROM kp_booking WHERE orderNo = ?";
            return $this->db->getRow($sql, [$orderNo]);
        } catch (Exception $e) {
            error_log("Failed to get booking by order number: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find new booking created after a change
     *
     * @param string $name Customer name
     * @param string $phone Customer phone
     * @param string $agent Agent name
     * @param string $originalCreateDate Original booking create date
     * @return array|bool New booking data if found, false otherwise
     */
    public function findNewBookingAfterChange($name, $phone, $agent, $originalCreateDate) {
        try {
            $sql = "SELECT use_date, orderNo FROM kp_booking
                    WHERE name = ?
                    AND phone = ?
                    AND agent = ?
                    AND create_date > ?
                    AND book_status != 'Change'
                    AND book_status != 'Cancel'
                    ORDER BY create_date ASC
                    LIMIT 1";

            return $this->db->getRow($sql, [$name, $phone, $agent, $originalCreateDate]);
        } catch (Exception $e) {
            error_log("Failed to find new booking after change: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a new booking
     * 
     * @param array $bookingData Booking data
     * @return string|bool Order number if created, false otherwise
     */
    public function createBooking($bookingData) {
        try {
            // Generate order number
            $orderNo = $this->generateOrderNo();
            $bookingData['orderNo'] = $orderNo;
            
            // Set create date
            $bookingData['create_date'] = date('Y-m-d H:i:s');
            
            // Insert booking
            $this->db->insert('kp_booking', $bookingData);
            
            // Create JSON file for this date
            $this->createJsonFileByDate($bookingData['use_date']);
            
            return $orderNo;
        } catch (Exception $e) {
            error_log("Failed to create booking: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a booking
     * 
     * @param int $bookingId Booking ID
     * @param array $bookingData Booking data
     * @return bool True if successful, false otherwise
     */
    public function updateBooking($bookingId, $bookingData) {
        try {
            // Update booking
            $this->db->update('kp_booking', $bookingData, 'booking_id = ?', [$bookingId]);
            
            // Get the updated booking to get the use_date
            $updatedBooking = $this->getBookingById($bookingId);
            
            // Create JSON file for this date
            if ($updatedBooking) {
                $this->createJsonFileByDate($updatedBooking['use_date']);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Failed to update booking: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a booking
     * 
     * @param int $bookingId Booking ID
     * @return bool True if successful, false otherwise
     */
    public function deleteBooking($bookingId) {
        try {
            // Get the booking to get the use_date before deleting
            $booking = $this->getBookingById($bookingId);
            
            // Delete booking
            $this->db->delete('kp_booking', 'booking_id = ?', [$bookingId]);
            
            // Create JSON file for this date
            if ($booking) {
                $this->createJsonFileByDate($booking['use_date']);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Failed to delete booking: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Generate a unique order number
     * 
     * @return string Order number
     */
    private function generateOrderNo() {
        $year = date('Y');
        $month = date('m');
        $prefix = "SWD{$year}{$month}";
        
        // Query to find the highest orderNo this month
        $sql = "SELECT orderNo FROM kp_booking WHERE orderNo LIKE ? ORDER BY orderNo DESC LIMIT 1";
        $searchPattern = $prefix . '%';
        $lastOrderNo = $this->db->getValue($sql, [$searchPattern]);
        
        if (!$lastOrderNo) {
            $newOrderNo = $prefix . "0001";
        } else {
            $lastOrderNo = substr($lastOrderNo, -4);
            $newOrderNo = $prefix . str_pad($lastOrderNo + 1, 4, '0', STR_PAD_LEFT);
        }
        
        return $newOrderNo;
    }
    
    /**
     * Create JSON file for bookings by date
     * 
     * @param string $useDate Date in Y-m-d format
     * @return bool True if successful, false otherwise
     */
    public function createJsonFileByDate($useDate) {
        try {
            // Create folder by month
            $month = date('m', strtotime($useDate));
            $year = date('Y', strtotime($useDate));
            $folder = dirname(__DIR__) . '/json/' . $year . '/' . $month;
            
            if (!file_exists($folder)) {
                mkdir($folder, 0777, true);
            }
            
            // Change date format to dd-mm-yyyy
            $useDateFile = date('d', strtotime($useDate));
            $filename = $folder . '/jsonBookingBy_' . $useDateFile . '.json';
            
            // Get bookings for this date
            $bookings = $this->getBookingsByDate($useDate);
            
            if (count($bookings) > 0) {
                $formattedBookings = [];
                
                foreach ($bookings as $row) {
                    $booking = [
                        'Order No' => $row['orderNo'],
                        'BookID' => $row['booking_id'],
                        'ZoneID' => $row['zone_id'],
                        'Use Date' => $row['use_date'],
                        'Amount' => $row['amount'],
                        'Create Date' => $row['create_date'],
                        'Customer' => $row['name'],
                        'Phone' => $row['phone'],
                        'Guests' => [
                            'Adults' => $row['adult'],
                            'Children' => $row['child'],
                            'Infants' => $row['infant']
                        ],
                        'Guide' => $row['guide'],
                        'FOC' => $row['inspection'],
                        'TL' => $row['team_leader'],
                        'Voucher No' => $row['voucher'],
                        'Agent' => $row['agent'],
                        'Request' => $row['remark'],
                        'Special' => $row['special_request'],
                        'Floor' => $row['use_zone'],
                        'Table' => $row['tables'],
                        'user_key' => $row['user_key'] ?? '',
                        'book_status' => $row['book_status'] ?? 'Pending',
                        'payment_status' => $row['payment_status'] ?? 'WP',
                        'payment_type' => $row['payment_type'] ?? 'Transfer'
                    ];
                    
                    $formattedBookings[] = $booking;
                }
                
                $json = json_encode($formattedBookings, JSON_PRETTY_PRINT);
                file_put_contents($filename, $json);
            } else {
                $json = json_encode([], JSON_PRETTY_PRINT);
                file_put_contents($filename, $json);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Failed to create JSON file by date: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get zones
     * 
     * @param int $cruiseId Cruise ID
     * @return array Zones data
     */
    public function getZones($cruiseId = 1) {
        try {
            $sql = "SELECT * FROM kp_booking_zones WHERE cruise_id = ? ORDER BY q ASC";
            return $this->db->getRows($sql, [$cruiseId]);
        } catch (Exception $e) {
            error_log("Failed to get zones: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get tables
     * 
     * @param string $status Table status (active, disabled)
     * @return array Tables data
     */
    public function getTables($status = 'active') {
        try {
            $sql = "SELECT * FROM kp_tables WHERE status = ? ORDER BY table_id ASC";
            return $this->db->getRows($sql, [$status]);
        } catch (Exception $e) {
            error_log("Failed to get tables: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get booked tables for a date
     * 
     * @param string $date Date in Y-m-d format
     * @return array Booked tables
     */
    public function getBookedTables($date) {
        try {
            $sql = "SELECT tables FROM kp_booking WHERE use_date = ? AND tables IS NOT NULL AND tables != ''";
            $rows = $this->db->getRows($sql, [$date]);
            
            $bookedTables = [];
            
            foreach ($rows as $row) {
                // Remove curly braces and split by comma
                $tables = str_replace(['{', '}'], '', $row['tables']);
                $tableArray = explode(',', $tables);
                
                // Add each table to the booked tables array
                foreach ($tableArray as $table) {
                    $table = trim($table);
                    if (!empty($table) && !in_array($table, $bookedTables)) {
                        $bookedTables[] = $table;
                    }
                }
            }
            
            return $bookedTables;
        } catch (Exception $e) {
            error_log("Failed to get booked tables: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get disabled tables
     * 
     * @param string $date Date in Y-m-d format
     * @return array Disabled tables
     */
    public function getDisabledTables($date) {
        try {
            $sql = "SELECT table_id, disabled_until FROM kp_tables WHERE status = 'disabled' AND (disabled_until IS NULL OR disabled_until >= ?)";
            return $this->db->getRows($sql, [$date]);
        } catch (Exception $e) {
            error_log("Failed to get disabled tables: " . $e->getMessage());
            return [];
        }
    }
}
