<div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="side-mini-panel with-vertical">
        <!-- ---------------------------------- -->
        <!-- Start Vertical Layout Sidebar -->
        <!-- ---------------------------------- -->
        <div class="iconbar">
            <div>

                <?php
                    include_once("../_menuMain.php");
                    include_once("_menuPages.php");
                ?>

            </div>
        </div>
    </aside>
    <!--  Sidebar End -->


    <div class="page-wrapper">
        <?php
            include_once("_menuTop.php");
        ?>

        <div class="body-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-3">
                        <div class="card text-bg-primary">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-7">
                                        <div class="d-flex flex-column h-20">
                                            <div class="hstack gap-3">

                                                <div class="row">
                                                    <div class="col-12">
                                                        <input type="date"
                                                            class="form-control text-white fs-6 text-nowrap" id="myDate"
                                                            name="myDate" value="<?php echo $toDaydefault; ?>" style="font-weight: bold;background-color: whitesmoke;color:
                                                        #635bff !important;width: 56px;padding: 15px;" />

                                                        <h5 class="text-white fs-6 mt-3 text-nowrap" id="shortDate">
                                                        </h5>
                                                        <h5 class="text-white fs-6 mt-0 text-nowrap" id="fullDate">
                                                        </h5>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-5 text-center text-md-end">
                                        <img src="../assets/images/backgrounds/welcome-bg.png" alt="welcome"
                                            class="img-fluid mb-n7 mt-2" width="180">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9">                        

                        <!-- add summary -->
                         <div class="card card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title mb-0">
                                    <i class="ti ti-chart-bar me-2"></i>Daily Summary
                                </h5>
                                <small class="text-muted" id="summary-date">Select a date to view summary</small>
                            </div>
                            <div class="table-responsive">
                                <table class="table search-table align-middle text-nowrap">
                                    <thead class="header-item">
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">Bookings</th>
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">Total Amount</th>

                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">Total Guests</th>
                                        
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">Adult</th>
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">Child</th>
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">Infant</th>
                                        
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">Guide</th>
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">FOC</th>
                                        <th class="text-dark fw-normal" style="font-weight: bold; text-align: center;">T/L</th>
                                    </thead>
                                    <tbody id="summary">
                                        <tr id="summary-data" class="table-info">
                                            <td id="total-bookings" class="fw-bold text-dark">0</td>
                                            <td id="total-amount" class="fw-bold text-success">฿0.00</td>

                                            <td id="total-guests" class="fw-bold text-dark">0</td>

                                            <td id="total-adult" class="fw-bold text-primary">0</td>
                                            <td id="total-child" class="fw-bold text-primary">0</td>
                                            <td id="total-infant" class="fw-bold text-primary">0</td>

                                            <td id="total-guide" class="fw-bold text-info">0</td>
                                            <td id="total-foc" class="fw-bold text-info">0</td>
                                            <td id="total-tl" class="fw-bold text-info">0</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>

                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-7">
                                        <form class="position-relative">
                                            <input type="text" class="form-control product-search ps-5"
                                                id="input-search" placeholder="Search by name, phone, or order number..." />
                                            <i
                                                class="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                        </form>
                                    </div>
                                    <div class="col-5 text-end">
                                        <button type="button" class="btn btn-success me-2" id="btn-export-excel" title="Export to Excel">
                                            <i class="ti ti-file-spreadsheet me-1"></i>
                                            Excel
                                        </button>
                                        <button type="button" class="btn btn-primary" id="btn-print-pdf-1st-floor" title="Download PDF">
                                            <i class="ti ti-download me-1"></i>
                                            Fl. 1
                                        </button>
                                        <button type="button" class="btn btn-primary" id="btn-print-pdf-2nd-floor" title="Download PDF">
                                            <i class="ti ti-download me-1"></i>
                                            Fl. 2
                                        </button>
                                        <button type="button" class="btn btn-primary" id="btn-print-pdf-3rd-floor" title="Download PDF">
                                            <i class="ti ti-download me-1"></i>
                                            Fl. 3
                                        </button>
                                        <button type="button" class="btn btn-primary" id="btn-print-pdf-all" title="Download PDF">
                                            <i class="ti ti-download me-1"></i>
                                            All
                                        </button>
                                        <?php if (isset($_SESSION['role']) && ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super_admin')): ?>
                                        <!-- <a href="edit_logs.php" class="btn btn-info" title="View Edit Logs">
                                            <i class="ti ti-history me-1"></i>
                                            Logs
                                        </a> -->
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="widget-content searchable-container list">                    
                    <div class="card card-body">
                        <div class="table-responsive">
                            <table class="table search-table align-middle text-nowrap table-hover">
                                
                                <thead class="header-item">
                                    <!-- <th>
                                        <div class="n-chk align-self-center text-center">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input primary"
                                                    id="contact-check-all" />
                                                <label class="form-check-label" for="contact-check-all"></label>
                                                <span class="new-control-indicator"></span>
                                            </div>
                                        </div>
                                    </th> -->
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Order No.</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Customer</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Adult / Child / Infant</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Guide / FOC / T/L</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Fl.</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Tables</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Amount</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Status</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Payment</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Pay Type</th>
                                    <!-- <th class="text-dark fw-normal" style="font-size: smaller;">Credit Term</th> -->
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Voucher No.</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Agent</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Remark</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">SP Note</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;">Use Date</th>
                                    <th class="text-dark fw-normal" style="font-size: smaller;"></th>
                                </thead>
                                <tbody id="output">
                                    <!-- Loading message will be replaced by JavaScript -->
                                    <tr>
                                        <td colspan="12" class="text-center">Loading bookings...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="offcanvas customizer offcanvas-end" tabindex="-1" id="offcanvasExample"
            aria-labelledby="offcanvasExampleLabel">
            <div class="d-flex align-items-center justify-content-between p-3 border-bottom">
                <h4 class="offcanvas-title fw-semibold" id="offcanvasExampleLabel">
                    Settings
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body" data-simplebar style="height: calc(100vh - 80px)">
                <h6 class="fw-semibold fs-4 mb-2">Theme</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check light-layout" name="theme-layout" id="light-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="light-layout">
                        <i class="icon ti ti-brightness-up fs-7 me-2"></i>Light
                    </label>

                    <input type="radio" class="btn-check dark-layout" name="theme-layout" id="dark-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="dark-layout">
                        <i class="icon ti ti-moon fs-7 me-2"></i>Dark
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Direction</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="direction-l" id="ltr-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="ltr-layout">
                        <i class="icon ti ti-text-direction-ltr fs-7 me-2"></i>LTR
                    </label>

                    <input type="radio" class="btn-check" name="direction-l" id="rtl-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="rtl-layout">
                        <i class="icon ti ti-text-direction-rtl fs-7 me-2"></i>RTL
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Colors</h6>

                <div class="d-flex flex-row flex-wrap gap-3 customizer-box color-pallete" role="group">
                    <input type="radio" class="btn-check" name="color-theme-layout" id="Blue_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Blue_Theme')" for="Blue_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="BLUE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-1">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Aqua_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Aqua_Theme')" for="Aqua_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="AQUA_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-2">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Purple_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Purple_Theme')" for="Purple_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="PURPLE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-3">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="green-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Green_Theme')" for="green-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="GREEN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-4">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="cyan-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Cyan_Theme')" for="cyan-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="CYAN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-5">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="orange-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Orange_Theme')" for="orange-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="ORANGE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-6">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Layout Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="vertical-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="vertical-layout">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Vertical
                        </label>
                    </div>
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="horizontal-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="horizontal-layout">
                            <i class="icon ti ti-layout-navbar fs-7 me-2"></i>Horizontal
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Container Option</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="layout" id="boxed-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="boxed-layout">
                        <i class="icon ti ti-layout-distribute-vertical fs-7 me-2"></i>Boxed
                    </label>

                    <input type="radio" class="btn-check" name="layout" id="full-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="full-layout">
                        <i class="icon ti ti-layout-distribute-horizontal fs-7 me-2"></i>Full
                    </label>
                </div>

                <h6 class="fw-semibold fs-4 mb-2 mt-5">Sidebar Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <a href="javascript:void(0)" class="fullsidebar">
                        <input type="radio" class="btn-check" name="sidebar-type" id="full-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="full-sidebar">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Full
                        </label>
                    </a>
                    <div>
                        <input type="radio" class="btn-check" name="sidebar-type" id="mini-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="mini-sidebar">
                            <i class="icon ti ti-layout-sidebar fs-7 me-2"></i>Collapse
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Card With</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="card-layout" id="card-with-border" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-with-border">
                        <i class="icon ti ti-border-outer fs-7 me-2"></i>Border
                    </label>

                    <input type="radio" class="btn-check" name="card-layout" id="card-without-border"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-without-border">
                        <i class="icon ti ti-border-none fs-7 me-2"></i>Shadow
                    </label>
                </div>
            </div>
        </div>

        <script>
            function handleColorTheme(e) {
                document.documentElement.setAttribute("data-color-theme", e);
            }
        </script>
    </div>


</div>

<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1" aria-labelledby="editBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editBookingModalLabel">Edit Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editBookingForm">
                    <input type="hidden" id="edit-booking-id" name="booking_id">
                    <input type="hidden" id="data-id" name="dataid">

                    <!-- Add booking Number and Booking Status Pending, Complete, Cancel, Change -->
                     <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="edit-order-no" class="form-label">Order No.</label>
                            <input type="text" class="form-control" id="edit-order-no" name="order_no" placeholder="Enter order number" disabled readonly>
                        </div>
                        <div class="col-md-4">
                            <label for="edit-booking-status" class="form-label">Booking Status</label><br/>
                            <span id="edit-booking-status" class="badge bg-info"></span> <!-- Booking status will be populated by JavaScript -->                            
                        </div>
                        <div class="col-md-4">
                            <label for="edit-booking-by" class="form-label">Booking by</label><br/>
                            <span id="edit-booking-by" class="badge bg-info"></span> <!-- Booking by will be populated by JavaScript -->
                        </div>
                    </div>


                    <!-- Customer Name and Phone Row -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit-customer-name" class="form-label">Customer Name</label>
                            <input type="text" class="form-control" id="edit-customer-name" name="customer_name" placeholder="Enter customer name">
                        </div>
                        <div class="col-md-6">
                            <label for="edit-phone" class="form-label">Phone</label>
                            <input type="text" class="form-control" id="edit-phone" name="phone" placeholder="Enter phone number">
                        </div>
                    </div>

                    <!-- Guest Count Row -->
                    <div class="row mb-3">
                        <div class="col-md-2">
                            <label for="edit-adults" class="form-label">Adults</label>
                            <input type="number" class="form-control text-center" id="edit-adults" name="adults" min="0" value="0">
                        </div>
                        <div class="col-md-2">
                            <label for="edit-children" class="form-label">Children</label>
                            <input type="number" class="form-control text-center" id="edit-children" name="children" min="0" value="0">
                        </div>
                        <div class="col-md-2">
                            <label for="edit-infants" class="form-label">Infants</label>
                            <input type="number" class="form-control text-center" id="edit-infants" name="infants" min="0" value="0">
                        </div>
                        <div class="col-md-2">
                            <label for="edit-guide" class="form-label">Guide</label>
                            <input type="number" class="form-control text-center" id="edit-guide" name="guide" min="0" value="0">
                        </div>
                        <div class="col-md-2">
                            <label for="edit-foc" class="form-label">FOC</label>
                            <input type="number" class="form-control text-center" id="edit-foc" name="foc" min="0" value="0">
                        </div>
                        <div class="col-md-2">
                            <label for="edit-leader" class="form-label">T/L</label>
                            <input type="number" class="form-control text-center" id="edit-leader" name="leader" min="0" value="0">
                        </div>
                    </div>

                    <!-- Voucher and Agent Row -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit-voucher" class="form-label">Voucher</label>
                            <input type="text" class="form-control" id="edit-voucher" name="voucher" placeholder="Enter voucher code">
                        </div>
                        <div class="col-md-6">
                            <label for="edit-agent" class="form-label">Agent</label>
                            <select class="form-select" id="edit-agent" name="agent">
                                <option value="">Select Agent</option>
                                <!-- Agent options will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <!-- Amount and Remark Row -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-dark">Amount</label>
                            <input type="number" name="amount" id="edit-amount" class="form-control fw-bold text-center" min="0" value="0" placeholder="Enter amount" style="font-size: x-large;">
                        </div>

                        <!-- Remark -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-dark">Remark</label>
                            <textarea name="remark" id="edit-remark" class="form-control" rows="1" placeholder="Enter any remarks"></textarea>
                        </div>                    
                    </div>

                    <!-- Special Request -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label">Special Request</label>
                            <div class="d-flex gap-4 mt-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_special_request" id="edit-special-none" value="0" checked>
                                    <label class="form-check-label" for="edit-special-none">
                                        None
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_special_request" id="edit-special-birthday" value="1">
                                    <label class="form-check-label" for="edit-special-birthday">
                                        🎂 Birthday
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_special_request" id="edit-special-anniversary" value="2">
                                    <label class="form-check-label" for="edit-special-anniversary">
                                        🍷 Anniversary
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="col-6">
                            <label for="edit-special-request-note" class="form-label">Special Request Note</label>
                            <textarea class="form-control" id="edit-special-request-note" name="special_request_note" rows="1" placeholder="Enter any special requests"></textarea>
                        </div>
                    </div>


                    <!-- Amount and Payment Type Row -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label">Payment Type</label>
                            <div class="d-flex gap-3 mt-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_payment_type" id="edit-payment-transfer" value="Transfer" checked>
                                    <label class="form-check-label" for="edit-payment-transfer">
                                        🏦 Transfer
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_payment_type" id="edit-payment-credit" value="Credit Card">
                                    <label class="form-check-label" for="edit-payment-credit">
                                        💳 Credit Card
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_payment_type" id="edit-payment-cash" value="Cash">
                                    <label class="form-check-label" for="edit-payment-cash">
                                        💵 Cash
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="edit_payment_type" id="edit-payment-credit-term" value="Credit Term">
                                    <label class="form-check-label" for="edit-payment-credit-term">
                                        📋 Credit Term
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- Payment Type -->
                    </div>

                    <!-- Date and Selected Tables Row -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="edit-date" class="form-label">User Date</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="edit-date" name="date" disabled readonly>
                                <!-- <button class="btn btn-outline-secondary" type="button" id="edit-date-picker-btn" title="Open Calendar">
                                    <i class="ti ti-calendar"></i>
                                </button> -->
                            </div>
                            <input type="hidden" id="c-useZone" name="c-useZone" value="1">
                        </div>
                        <div class="col-md-8">
                            <label class="form-label">Selected Tables:</label>
                            <div class="border rounded p-2 bg-light">
                                <div id="edit-selected-tables" class="d-flex flex-wrap gap-1">
                                    <!-- Selected tables badges will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="save-edit-booking">Save Booking</button>
            </div>
        </div>
    </div>
</div>