<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

require_once('../dbconnect/_dbconnect.php');

// Check if we can use TCPDF or create a simple HTML to PDF solution
function generatePDF($date, $searchTerm = '') {
    try {
        // Get database connection
        $conn = db_connect();
        
        // Convert date format from dd-mm-yyyy to yyyy-mm-dd for database query
        $dateParts = explode('-', $date);
        if (count($dateParts) === 3) {
            $dbDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];
        } else {
            $dbDate = date('Y-m-d');
        }

        // Get bookings from database - Filter for 1st floor only (use_zone = '1')
        $sql = "SELECT * FROM kp_booking WHERE use_date = :date AND use_zone = '1'";
        $params = [':date' => $dbDate];

        // Add search filter if provided
        if (!empty($searchTerm)) {
            $sql .= " AND (name LIKE :search OR phone LIKE :search OR orderNo LIKE :search)";
            $params[':search'] = '%' . $searchTerm . '%';
        }

        $sql .= " ORDER BY booking_id ASC";

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // print_r($bookings);
        // exit;

        // set print to landscape
        $orientation = 'L';
        $size = 'A4';

        // zoom out 60%
        $zoom = 0.6;

        // Generate HTML content for PDF
        $html = generateHTMLContent($date, $bookings, $searchTerm);
        
        // Set headers for PDF download
        $filename = "1st_Floor_Bookings_" . str_replace('-', '_', $date);
        if (!empty($searchTerm)) {
            $filename .= "_filtered";
        }
        $filename .= ".pdf";

        // Output HTML for browser printing
        header('Content-Type: text/html; charset=utf-8');
        echo $html;
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
    }
}

function generateHTMLContent($date, $bookings, $searchTerm) {
    $totalBookings = count($bookings);
    $totalAmount = 0;
    $totalAdults = 0;
    $totalChildren = 0;
    $totalInfants = 0;
    $totalGuide = 0;
    $totalFOC = 0;
    $totalTL = 0;

    // Process and sort tables from bookings
    $bookedTables = [];

    // Calculate totals and collect table data
    foreach ($bookings as $booking) {
        $totalAmount += floatval($booking['amount'] ?? 0);
        $totalAdults += intval($booking['adult'] ?? 0);
        $totalChildren += intval($booking['child'] ?? 0);
        $totalInfants += intval($booking['infant'] ?? 0);
        $totalGuide += intval($booking['guide'] ?? 0);
        $totalFOC += intval($booking['inspection'] ?? 0);
        $totalTL += intval($booking['team_leader'] ?? 0);

        // Process tables for this booking
        if (!empty($booking['tables'])) {
            $tables = explode(',', $booking['tables']);
            foreach ($tables as $table) {
                $table = trim($table);
                if (!empty($table)) {
                    $bookedTables[$table] = [
                        'name' => $booking['name'],
                        'adult' => intval($booking['adult'] ?? 0),
                        'child' => intval($booking['child'] ?? 0),
                        'special_request' => intval($booking['special_request'] ?? 0),
                        'agent' => $booking['agent'] ?? '',
                        'orderNo' => $booking['orderNo'] ?? ''
                    ];
                }
            }
        }
    }

    // Sort tables by ID
    ksort($bookedTables);

    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Booking Report - ' . htmlspecialchars($date) . '</title>
    <style>
        @page {
            size: ' . $size . ' landscape;
            margin: 10mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 9px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .summary h3 {
            margin-top: 0;
            font-size: 14px;
        }
        .summary-grid-1 {
            display: grid;
            grid-template-columns: repeat(11, 1fr);
            gap: 10px;
            margin-top: 5px;
        }
        .summary-grid-2 {
            display: grid;
            grid-template-columns: repeat(20, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        .summary-item {
            text-align: center;
            padding: 3px;
            background-color: #e8e8e8;
            border-radius: 3px;
            box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.1);
        }
        .summary-item-none {
            text-align: center;
            padding: 3px;
            // background-color: white;
            border-radius: 3px;
        }
        .summary-item-none2 {
            text-align: center;
            padding: 3px;
            background-color: white;
            border-radius: 3px;
        }    
        .summary-item strong {
            display: block;
            font-size: 13px;
            color: #007bff;
            margin-bottom: 2px;
        }
        .no-print {
            display: none;
        }
        .floorplan-section {
            margin-top: 20px;
            text-align: center;
        }
        .floorplan-image {
            width: 80%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sawasdee Booking Report - 1st Floor</h1>
        <p>Date: ' . htmlspecialchars($date) . '</p>
        <p>Generated on: ' . date('d-m-Y H:i:s') . '</p>
    </div>';
    
    // Generate dynamic floor plan based on booked tables
    $html .= '
    <div class="summary">
        <h3>Floor 1 - Booked Tables</h3>';

    if (empty($bookedTables)) {
        $html .= '<p style="text-align: center; padding: 20px;">No tables booked for this date.</p>';
    } else {

        $tableCount = 0;

        $tableIds = array(
            array('C12', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23'),
            array('C12-1', 'C14-1', 'C15-1', 'C16-1', 'C17-1', 'C18-1', 'C19-1', 'C20-1', 'C21-1', 'C22-1', 'C23-1'),
            array('C12-2', 'C14-2', 'C15-2', 'C16-2', 'C17-2', 'C18-2', 'C19-2', 'C20-2', 'C21-2', 'C22-2', 'C23-2'),
            array('C12-3', 'C14-3', 'C15-3', 'C16-3', 'C17-3', 'C18-3', 'C19-3', 'C20-3', 'C21-3', 'C22-3', 'C23-3'),
            array('C12-4', 'C14-4', 'C15-4', 'C16-4', 'C17-4', 'C18-4', 'C19-4', 'C20-4', 'C21-4', 'C22-4', 'C23-4'),
            array('C12-5', 'C14-5', 'C15-5', 'C16-5', 'C17-5', 'C18-5', 'C19-5', 'C20-5', 'C21-5', 'C22-5', 'C23-5'),
            array('X01', 'X02', 'X03', 'X04', 'X05', 'X06', 'X07', 'X08', 'X09', 'X10', 'X11'),
            array('C1-5', 'C2-5', 'C3-5', 'C4-5', 'C5-5', 'C6-5', 'C7-5', 'C8-5', 'C9-5', 'C10-5', 'C11-5'),
            array('C1-4', 'C2-4', 'C3-4', 'C4-4', 'C5-4', 'C6-4', 'C7-4', 'C8-4', 'C9-4', 'C10-4', 'C11-4'),
            array('C1-3', 'C2-3', 'C3-3', 'C4-3', 'C5-3', 'C6-3', 'C7-3', 'C8-3', 'C9-3', 'C10-3', 'C11-3'),
            array('C1-2', 'C2-2', 'C3-2', 'C4-2', 'C5-2', 'C6-2', 'C7-2', 'C8-2', 'C9-2', 'C10-2', 'C11-2'),
            array('C1-1', 'C2-1', 'C3-1', 'C4-1', 'C5-1', 'C6-1', 'C7-1', 'C8-1', 'C9-1', 'C10-1', 'C11-1'),
            array('C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11')
        );

        $tableIdsNoneDisplay = array(
            "X01", "X02", "X03", "X04", "X05", "X06", "X07", "X08", "X09", "X10", "X11",
            "C12-4", "C12-5", "C17-5", "C18-5", "C19-5", "C20-5","C22-5",
            "C1-4", "C1-5", "C5-5", "C6-5", "C7-5", "C8-5", "C10-5"
        );

        foreach ($tableIds as $tableIdArray) {
            $html .= '<div class="summary-grid-1">';

            if ($tableCount > 0 && $tableCount % 11 == 0) {
                $html .= '</div><div class="summary-grid-1">';
            }

            foreach ($tableIdArray as $tableId) {
                if (array_key_exists($tableId, $bookedTables)) {
                    $tableData = $bookedTables[$tableId];
                    $totalPax = $tableData['adult'] + $tableData['child'];
                    $tableId = htmlspecialchars($tableId);
            
                    // Format special request icon
                    $specialIcon = '';
                    switch ($tableData['special_request']) {
                        case 1:
                            $specialIcon = '🎂';
                            break;
                        case 2:
                            $specialIcon = '🥂';
                            break;
                        default:
                            $specialIcon = '';
                    }

                    $html .= '
                    <div class="summary-item" id="' . htmlspecialchars($tableId) . '">
                        <span style="text-align: left; display: block;">' . htmlspecialchars($tableData['orderNo']) . '</span>
                        <strong>' . htmlspecialchars($tableId) . '</strong>                        
                        <span>'.$specialIcon.' '.$totalPax.' Pax</span><br/>
                        <span>' . htmlspecialchars($tableData['name']) . '</span>                        
                    </div>';
                } else {

                    if (in_array($tableId, $tableIdsNoneDisplay)) {
                        $html .= '
                        <div class="summary-item-none">
                            <span>&nbsp;</span>                        
                        </div>';
                    } else {
                        $html .= '
                        <div class="summary-item" id="' . htmlspecialchars($tableId) . '">
                            <strong>' . htmlspecialchars($tableId) . '</strong>
                            <span>&nbsp;</span><br/>
                            <span>&nbsp;</span><br/>
                            <span>&nbsp;</span>
                        </div>';
                    }
                }
                $tableCount++;
            }

            $html .= '</div>';
        }
    }

    // Add summary section
    /*
    $html .= '
    <div class="summary">
        <h3>Summary</h3>
        <div class="summary-grid-1">
            <div class="summary-item">
                <strong>' . $totalBookings . '</strong>
                <span>Total Bookings</span>
            </div>
            <div class="summary-item">
                <strong>฿' . number_format($totalAmount, 2) . '</strong>
                <span>Total Amount</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalAdults . '</strong>
                <span>Total Adults</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalChildren . '</strong>
                <span>Total Children</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalInfants . '</strong>
                <span>Total Infants</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalGuide . '</strong>
                <span>Total Guide</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalFOC . '</strong>
                <span>Total FOC</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalTL . '</strong>
                <span>Total TL</span>
            </div>
        </div>
    </div>';
    */

    $html .= '
    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>';

    return $html;
}

// Main execution
try {
    $date = $_GET['date'] ?? date('d-m-Y');
    $searchTerm = $_GET['search'] ?? '';
    
    generatePDF($date, $searchTerm);
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>