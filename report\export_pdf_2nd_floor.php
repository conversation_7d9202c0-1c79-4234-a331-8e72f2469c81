<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

require_once('../dbconnect/_dbconnect.php');

// Try to include mPDF library if available
$mpdfAvailable = false;
if (file_exists('../vendor/autoload.php')) {
    require_once('../vendor/autoload.php');
    $mpdfAvailable = class_exists('\Mpdf\Mpdf');
}

function generatePDF($date, $searchTerm = '') {
    try {
        // Get database connection
        $conn = db_connect();

        // Convert date format from dd-mm-yyyy to yyyy-mm-dd for database query
        $dateParts = explode('-', $date);
        if (count($dateParts) === 3) {
            $dbDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];
        } else {
            $dbDate = date('Y-m-d');
        }

        // Get bookings from database - Filter for 2nd floor only (use_zone = '2')
        $sql = "SELECT * FROM kp_booking WHERE use_date = :date AND use_zone = '2'";
        $params = [':date' => $dbDate];

        // Add search filter if provided
        if (!empty($searchTerm)) {
            $sql .= " AND (name LIKE :search OR phone LIKE :search OR orderNo LIKE :search)";
            $params[':search'] = '%' . $searchTerm . '%';
        }

        $sql .= " ORDER BY booking_id ASC";

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Generate HTML content for PDF
        $html = generateHTMLContent($date, $bookings, $searchTerm);

        // Set filename for download
        $filename = "2nd_Floor_Bookings_" . str_replace('-', '_', $date);
        if (!empty($searchTerm)) {
            $filename .= "_filtered";
        }

        global $mpdfAvailable;

        if ($mpdfAvailable) {
            // Use mPDF if available
            try {
                $mpdf = new \Mpdf\Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4-L', // A4 Landscape
                    'orientation' => 'L',
                    'margin_left' => 10,
                    'margin_right' => 10,
                    'margin_top' => 10,
                    'margin_bottom' => 10,
                    'margin_header' => 5,
                    'margin_footer' => 5
                ]);

                $mpdf->SetTitle('2nd Floor Bookings - ' . $date);
                $mpdf->SetAuthor('Sawasdee Booking System');
                $mpdf->SetSubject('Booking Report');

                // Write HTML to PDF
                $mpdf->WriteHTML($html);

                // Output PDF as download
                $mpdf->Output($filename . ".pdf", 'D'); // 'D' = Download
                return;

            } catch (Exception $mpdfError) {
                // Fall through to HTML fallback
            }
        }

        // Fallback: Output as HTML file for download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.html"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // Add print styles and auto-print functionality for HTML fallback
        $html = str_replace('</head>', '
        <style>
            @media print {
                .no-print { display: none !important; }
                body { -webkit-print-color-adjust: exact; }
            }
        </style>
        <script>
            // Auto-print when page loads (for HTML fallback)
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        </script>
        </head>', $html);

        echo $html;

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
}

function generateHTMLContent($date, $bookings, $searchTerm) {
    $totalBookings = count($bookings);
    $totalAmount = 0;
    $totalAdults = 0;
    $totalChildren = 0;
    $totalInfants = 0;
    $totalGuide = 0;
    $totalFOC = 0;
    $totalTL = 0;

    // Process and sort tables from bookings
    $bookedTables = [];

    // Calculate totals and collect table data
    foreach ($bookings as $booking) {
        $totalAmount += floatval($booking['amount'] ?? 0);
        $totalAdults += intval($booking['adult'] ?? 0);
        $totalChildren += intval($booking['child'] ?? 0);
        $totalInfants += intval($booking['infant'] ?? 0);
        $totalGuide += intval($booking['guide'] ?? 0);
        $totalFOC += intval($booking['inspection'] ?? 0);
        $totalTL += intval($booking['team_leader'] ?? 0);

        // Process tables for this booking
        if (!empty($booking['tables'])) {
            $tables = explode(',', $booking['tables']);
            foreach ($tables as $table) {
                $table = trim($table);
                if (!empty($table)) {
                    $bookedTables[$table] = [
                        'name' => $booking['name'],
                        'adult' => intval($booking['adult'] ?? 0),
                        'child' => intval($booking['child'] ?? 0),
                        'special_request' => intval($booking['special_request'] ?? 0),
                        'agent' => $booking['agent'] ?? '',
                        'orderNo' => $booking['orderNo'] ?? '',
                        'remark' => $booking['remark'] ?? '',
                    ];
                }
            }
        }
    }

    // Sort tables by ID
    ksort($bookedTables);

    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Booking Report - ' . htmlspecialchars($date) . '</title>
    <style>
        @page {
            size: ' . $size . ' landscape;
            margin: 10mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 9px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .summary h3 {
            margin-top: 0;
            font-size: 14px;
        }
        .summary-grid-1 {
            display: grid;
            grid-template-columns: repeat(21, 1fr);
            gap: 10px;
            margin-top: 5px;
        }
        .summary-item {
            text-align: center;
            padding: 3px;
            background-color: #e8e8e8;
            border-radius: 3px;
            box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.1);
        }
        .summary-item-none {
            text-align: center;
            padding: 3px;
            // background-color: white;
            border-radius: 3px;
        }
        .summary-item-none2 {
            text-align: center;
            padding: 3px;
            background-color: white;
            border-radius: 3px;
        }    
        .summary-item strong {
            display: block;
            font-size: 13px;
            color: #007bff;
            margin-bottom: 2px;
        }
        .no-print {
            display: none;
        }
        .floorplan-section {
            margin-top: 20px;
            text-align: center;
        }
        .floorplan-image {
            width: 80%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sawasdee Booking Report - 2nd Floor</h1>
        <p>Date: ' . htmlspecialchars($date) . '</p>
        <p>Exported on: ' . date('d-m-Y H:i:s') . '</p>
    </div>';
    
    // Generate dynamic floor plan based on booked tables
    $html .= '
    <div class="summary">
        <h3>Floor 2 - Booked Tables</h3>';

    if (empty($bookedTables)) {
        $html .= '<p style="text-align: center; padding: 20px;">No tables booked for this date.</p>';
    } else {

        $tableCount = 0;

        $tableIds = array(
            array('x',     'x',    'x',     'H10',   'H11',   'H12',   'B12',   'B14',   'B15',   'B16',   'B17',   'B18',   'B19',   'B20',   'B21',   'B22',   'B23',   'B24',   'B25', 'B26'),
            array('x',     'x',    'H9',    'H10-1', 'H11-1', 'H12-1', 'B12-1', 'B14-1', 'B15-1', 'B16-1', 'B17-1', 'B18-1', 'B19-1', 'B20-1', 'B21-1', 'B22-1', 'B23-1', 'B24-1', 'x',   'x'),
            array('x',     'H8',   'H9-1',  'H10-2', 'H11-2', 'H12-2', 'B12-2', 'B14-2', 'B15-2', 'B16-2', 'B17-2', 'B18-2', 'B19-2', 'B20-2', 'B21-2', 'B22-2', 'B23-2', 'x',     'x',   'x'),
            array('x',     'H8-1', 'H9-2',  'H10-3', 'H11-3', 'H12-3', 'B12-3', 'B14-3', 'B15-3', 'B16-3', 'B17-3', 'B18-3', 'B19-3', 'B20-3', 'B21-3', 'B22-3', 'B23-3', 'x',     'x',   'x'),
            array('H70',   'H8-2', 'H9-3',  'x',     'H17',   'x',     'x',     'x',     'x',     'x',     'B17-4', 'B18-4', 'x',     'B20-4', 'x',     'B22-4', 'B23-4', 'x',     'x',   'x'),
            array('H7-1',  'H8-3', 'x',     'H16',   'H17-1', 'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',   'x'),
            array('H7-2',  'x',    'H15',   'H16-1', 'H17-2', 'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',   'x'),
            array('x',     'H14',  'x',     'H16-2', 'H17-3', 'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',   'x'),
            array('H1-2',  'x',    'H15-1', 'H16-3', 'H17-4', 'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',   'x'),
            array('H1-1',  'H2-3', 'x',     'H16-4', 'H17-5', 'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',   'x'),
            array('H1',    'H2-2', 'H3-3',  'x',     'H17-6', 'x',     'x',     'x',     'x',     'x',     'B5-4',  'B6-4',  'x',     'B8-4',  'x',     'x',     'B11-4', 'x',     'x',   'x'),
            array('x',     'H2-1', 'H3-2',  'H4-3',  'H5-3',  'H6-3',  'B1-3',  'B2-3',  'B3-3',  'B4-3',  'B5-3',  'B6-3',  'B7-3',  'B8-3',  'B9-3',  'B10-3', 'B11-3', 'x',     'x',   'x'),
            array('x',     'H2',   'H3-1',  'H4-2',  'H5-2',  'H6-2',  'B1-2',  'B2-2',  'B3-2',  'B4-2',  'B5-2',  'B6-2',  'B7-2',  'B8-2',  'B9-2',  'B10-2', 'B11-2', 'x',     'x',   'x'),
            array('x',     'x',    'H3',    'H4-1',  'H5-1',  'H6-1',  'B1-1',  'B2-1',  'B3-1',  'B4-1',  'B5-1',  'B6-1',  'B7-1',  'B8-1',  'B9-1',  'B10-1', 'B11-1', 'x',     'x',   'x'),
            array('x',     'x',    'x',     'H4',    'H5',    'H6',    'B1',    'B2',    'B3',    'B4',    'B5',    'B6',    'B7',    'B8',    'B9',    'B10',   'B11',   'x',     'x',   'x'),
        );

        $tableIdsNoneDisplay = array(
            "x"
        );

        foreach ($tableIds as $tableIdArray) {
            $html .= '<div class="summary-grid-1">';

            if ($tableCount > 0 && $tableCount % 11 == 0) {
                $html .= '</div><div class="summary-grid-1">';
            }

            foreach ($tableIdArray as $tableId) {
                if (array_key_exists($tableId, $bookedTables)) {
                    $tableData = $bookedTables[$tableId];
                    $totalPax = $tableData['adult'] + $tableData['child'];
                    $tableId = htmlspecialchars($tableId);
            
                    // Format special request icon
                    $specialIcon = '';
                    switch ($tableData['special_request']) {
                        case 1:
                            $specialIcon = '🎂';
                            break;
                        case 2:
                            $specialIcon = '🍸';
                            break;
                        default:
                            $specialIcon = '';
                    }

                    $html .= '
                    <div class="summary-item" style="width:70px; background-color:#e8e8e8;" id="' . htmlspecialchars($tableId) . '">
                        <span style="text-align: left; display: block; font-size: smaller">' . htmlspecialchars($tableData['orderNo']) . '</span>
                        <strong>' . htmlspecialchars($tableId) . '</strong>                        
                        <span>'.$specialIcon.' '.$totalPax.' Pax</span><br/>
                        <span>' . htmlspecialchars($tableData['name']) . '</span><br/>
                        <span>' . htmlspecialchars($tableData['remark']) . '</span><br/>
                        <span style="text-align: right; display: block; font-size: smaller;">' . htmlspecialchars($tableData['agent']) . '</span>
                    </div>';
                } else {

                    if (in_array($tableId, $tableIdsNoneDisplay)) {
                        $html .= '
                        <div class="summary-item-none" style="width:70px;">
                            <span>&nbsp;</span>                        
                        </div>';
                    } else {
                        $html .= '
                        <div class="summary-item" style="width:70px;" id="' . htmlspecialchars($tableId) . '">
                            <strong>' . htmlspecialchars($tableId) . '</strong>
                            <span>&nbsp;</span><br/>
                            <span>&nbsp;</span><br/>
                            <span>&nbsp;</span>
                        </div>';
                    }
                }
                $tableCount++;
            }

            $html .= '</div>';
        }
    }

    // Add summary section
    /*
    $html .= '
    <div class="summary">
        <h3>Summary</h3>
        <div class="summary-grid-1">
            <div class="summary-item">
                <strong>' . $totalBookings . '</strong>
                <span>Total Bookings</span>
            </div>
            <div class="summary-item">
                <strong>฿' . number_format($totalAmount, 2) . '</strong>
                <span>Total Amount</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalAdults . '</strong>
                <span>Total Adults</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalChildren . '</strong>
                <span>Total Children</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalInfants . '</strong>
                <span>Total Infants</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalGuide . '</strong>
                <span>Total Guide</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalFOC . '</strong>
                <span>Total FOC</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalTL . '</strong>
                <span>Total TL</span>
            </div>
        </div>
    </div>';
    */

    $html .= '
</body>
</html>';

    return $html;
}

// Main execution
try {
    $date = $_GET['date'] ?? date('d-m-Y');
    $searchTerm = $_GET['search'] ?? '';
    
    generatePDF($date, $searchTerm);
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>