
<?php
// Start session
session_start();

// Check if there's a login error
$login_error = isset($_SESSION['login_error']) ? $_SESSION['login_error'] : '';
unset($_SESSION['login_error']);

// Check if session expired
$session_expired = isset($_GET['session_expired']) ? true : false;
?>
<!DOCTYPE html>
<html lang="en" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Sawasdee Admin Login" />
  <meta name="robots" content="noindex, nofollow" />

  <!-- Basic security headers -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff" />
  <meta http-equiv="X-XSS-Protection" content="1; mode=block" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="../assets/images/logos/favicon.png" />

  <!-- Core Css -->
  <link rel="stylesheet" href="../assets/css/styles.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

  <!-- custom style -->
  <style>
    .swal2-timer-progress-bar {
      background-color: #28a745 !important; /* Bootstrap 'success' green */
    }
    .password-strength {
      margin-top: 5px;
      height: 5px;
      width: 100%;
      background: #ddd;
      border-radius: 3px;
    }
    .password-strength-bar {
      height: 100%;
      border-radius: 3px;
      transition: width 0.3s ease;
    }
    .weak { width: 25%; background-color: #ff4d4d; }
    .medium { width: 50%; background-color: #ffaa00; }
    .strong { width: 75%; background-color: #73e600; }
    .very-strong { width: 100%; background-color: #00b33c; }
    .password-feedback {
      font-size: 12px;
      margin-top: 5px;
      color: #666;
    }
    .error-message {
      color: #dc3545;
      font-size: 12px;
      margin-top: 5px;
      display: none;
    }
  </style>

  <title>Sawasdee Booking</title>
</head>

<body>
  <!-- Preloader -->
  <div class="preloader">
    <img src="../assets/images/logos/favicon.png" alt="loader" class="lds-ripple img-fluid" />
  </div>
  <div id="main-wrapper">
    <div class="position-relative overflow-hidden auth-bg min-vh-100 w-100 d-flex align-items-center justify-content-center">
      <div class="d-flex align-items-center justify-content-center w-100">
        <div class="row justify-content-center w-100 my-5 my-xl-0">
          <div class="col-4 d-flex flex-column justify-content-center">
            <div class="card mb-0 bg-body auth-login m-auto w-100">
              <div class="row gx-0">
                <!-- ------------------------------------------------- -->
                <!-- Part 1 -->
                <!-- ------------------------------------------------- -->
                <div class="col-12 border-end">
                  <div class="row justify-content-center py-4">
                    <div class="col-lg-11">
                      <div class="card-body">
                        <a href="./" class="text-nowrap logo-img d-block mb-4 w-100 text-center">
                          <img src="../assets/images/logos/logo.png" class="dark-logo" alt="Logo-Dark" />
                        </a>                        
                        <form id="loginForm" action="login_process.php" method="post" novalidate>
                          <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" name="username" class="form-control" id="username" placeholder="Enter your username" required autocomplete="username">
                            <div id="usernameError" class="error-message">Please enter a valid username</div>
                          </div>
                          <div class="mb-3">
                            <div class="d-flex align-items-center justify-content-between">
                              <label for="password" class="form-label">Password</label>
                            </div>
                            <input type="password" name="password" class="form-control" id="password" placeholder="Enter your password" required autocomplete="current-password">
                            <div id="passwordError" class="error-message">Please enter your password</div>

                            <!-- Password strength indicator -->
                            <div class="password-strength">
                              <div class="password-strength-bar" id="passwordStrengthBar"></div>
                            </div>
                            <div class="password-feedback" id="passwordFeedback"></div>
                          </div>

                          <!-- Login attempt error message -->
                          <?php if (!empty($login_error)): ?>
                          <div class="alert alert-danger mt-3"><?php echo $login_error; ?></div>
                          <?php endif; ?>

                          <!-- Session expired message -->
                          <?php if ($session_expired): ?>
                          <div class="alert alert-warning mt-3">Your session has expired. Please log in again.</div>
                          <?php endif; ?>
                          <!-- <div class="d-flex align-items-center justify-content-between mb-4">
                            <div class="form-check">
                              <input class="form-check-input primary" type="checkbox" value="" id="flexCheckChecked" checked>
                              <label class="form-check-label text-dark" for="flexCheckChecked">
                                Keep me logged in
                              </label>
                            </div>
                          </div> -->
                          <button type="submit" name="submit" id="btSubmit" class="btn btn-dark w-100 py-2 mb-4 rounded-1">
                            Sign In <span id="spinner" class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                          </button>

                          <!-- Reset Password Link -->
                          <!-- <div class="text-center mb-3">
                            <a href="reset_password.php" class="btn btn-outline-secondary w-100 py-2 rounded-1">
                              Reset Password
                            </a>
                          </div> -->

                          <!-- Security notice -->
                          <div class="text-center">
                            <small class="text-muted">
                              <i class="fa fa-lock"></i> Secure login protected with encryption
                            </small>
                          </div>
                          <!-- <submit type="submit" name="submit" class="btn btn-dark w-100 py-2 mb-4 rounded-1">Sign In</submit> -->
                          <!-- <a class="btn btn-dark w-100 py-8 mb-4 rounded-1" id="btSubmit">Sign In</a> -->
                          <!-- <div class="d-flex align-items-center">
                            <p class="fs-12 mb-0 fw-medium">Don’t have an account yet?</p>
                            <a class="text-primary fw-semibold ms-2" href="main/authentication-register2.html">Sign Up Now</a>
                          </div> -->
                        </form>
                      </div>
                    </div>
                  </div>

                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="dark-transparent sidebartoggler"></div>
  <!-- Import Js Files -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> <!-- Link to jQuery -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!-- Link to SweetAlert2 -->
  <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="../assets/js/theme/app.init.js"></script>
  <script src="../assets/js/theme/theme.js"></script>
  <script src="../assets/js/theme/app.min.js"></script>

  <script>
  $(document).ready(function() {
    // Password strength checker
    $("#password").on("input", function() {
      var password = $(this).val();
      var strength = 0;
      var feedback = "";

      if (password.length > 0) {
        // Check password length
        if (password.length >= 8) {
          strength += 1;
        } else {
          feedback += "Password should be at least 8 characters. ";
        }

        // Check for uppercase letters
        if (/[A-Z]/.test(password)) {
          strength += 1;
        } else {
          feedback += "Add uppercase letters. ";
        }

        // Check for lowercase letters
        if (/[a-z]/.test(password)) {
          strength += 1;
        } else {
          feedback += "Add lowercase letters. ";
        }

        // Check for numbers
        if (/[0-9]/.test(password)) {
          strength += 1;
        } else {
          feedback += "Add numbers. ";
        }

        // Check for special characters
        if (/[^A-Za-z0-9]/.test(password)) {
          strength += 1;
        } else {
          feedback += "Add special characters. ";
        }

        // Update strength bar
        var $strengthBar = $("#passwordStrengthBar");
        $strengthBar.removeClass("weak medium strong very-strong");

        if (strength === 0) {
          $strengthBar.css("width", "0%");
        } else if (strength <= 2) {
          $strengthBar.addClass("weak");
        } else if (strength <= 3) {
          $strengthBar.addClass("medium");
        } else if (strength <= 4) {
          $strengthBar.addClass("strong");
        } else {
          $strengthBar.addClass("very-strong");
        }

        // Update feedback
        $("#passwordFeedback").text(feedback);
      } else {
        $("#passwordStrengthBar").css("width", "0%").removeClass("weak medium strong very-strong");
        $("#passwordFeedback").text("");
      }
    });

    // Form validation
    $("#loginForm").on("submit", function(event) {
      var isValid = true;

      // Validate username
      var username = $("#username").val().trim();
      if (username === "") {
        $("#usernameError").show();
        isValid = false;
      } else {
        $("#usernameError").hide();
      }

      // Validate password
      var password = $("#password").val();
      if (password === "") {
        $("#passwordError").show();
        isValid = false;
      } else {
        $("#passwordError").hide();
      }

      if (!isValid) {
        event.preventDefault();
        return false;
      }

      // Show spinner and disable button
      $('#spinner').show();
      $('#btSubmit').prop('disabled', true);
    });

    // Prevent multiple form submissions
    $('form').on('submit', function() {
      $(this).find('button[type="submit"]').prop('disabled', true);
    });
  });
</script>



  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
</body>

</html>