<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

require_once('../dbconnect/_dbconnect.php');

// Try to include mPDF library if available
$mpdfAvailable = false;
if (file_exists('../vendor/autoload.php')) {
    require_once('../vendor/autoload.php');
    $mpdfAvailable = class_exists('\Mpdf\Mpdf');
}

function generatePDF($date, $searchTerm = '') {
    try {
        // Get database connection
        $conn = db_connect();

        // Convert date format from dd-mm-yyyy to yyyy-mm-dd for database query
        $dateParts = explode('-', $date);
        if (count($dateParts) === 3) {
            $dbDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];
        } else {
            $dbDate = date('Y-m-d');
        }

        // Get bookings from database - All floors
        $sql = "SELECT * FROM kp_booking WHERE use_date = :date";
        $params = [':date' => $dbDate];

        // Add search filter if provided
        if (!empty($searchTerm)) {
            $sql .= " AND (name LIKE :search OR phone LIKE :search OR orderNo LIKE :search)";
            $params[':search'] = '%' . $searchTerm . '%';
        }

        $sql .= " ORDER BY use_zone ASC, booking_id ASC";

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Generate HTML content for PDF
        $html = generateHTMLContent($date, $bookings, $searchTerm);

        // Set filename for download
        $filename = "All_Floors_Bookings_" . str_replace('-', '_', $date);
        if (!empty($searchTerm)) {
            $filename .= "_filtered";
        }

        global $mpdfAvailable;

        if ($mpdfAvailable) {
            // Use mPDF if available
            try {
                $mpdf = new \Mpdf\Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4-L', // A4 Landscape
                    'orientation' => 'L',
                    'margin_left' => 10,
                    'margin_right' => 10,
                    'margin_top' => 10,
                    'margin_bottom' => 10,
                    'margin_header' => 5,
                    'margin_footer' => 5
                ]);

                $mpdf->SetTitle('All Floors Bookings - ' . $date);
                $mpdf->SetAuthor('Sawasdee Booking System');
                $mpdf->SetSubject('Booking Report');

                // Write HTML to PDF
                $mpdf->WriteHTML($html);

                // Output PDF as download
                $mpdf->Output($filename . ".pdf", 'D'); // 'D' = Download
                return;

            } catch (Exception $mpdfError) {
                // Fall through to HTML fallback
            }
        }

        // Fallback: Output as HTML file for download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.html"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // Add print styles and auto-print functionality for HTML fallback
        $html = str_replace('</head>', '
        <style>
            @media print {
                .no-print { display: none !important; }
                body { -webkit-print-color-adjust: exact; }
            }
        </style>
        <script>
            // Auto-print when page loads (for HTML fallback)
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        </script>
        </head>', $html);

        echo $html;

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
}

function generateHTMLContent($date, $bookings, $searchTerm) {
    $totalBookings = count($bookings);
    $totalAmount = 0;
    $totalAdults = 0;
    $totalChildren = 0;
    $totalInfants = 0;
    $totalGuide = 0;
    $totalFOC = 0;
    $totalTL = 0;

    // Calculate totals
    foreach ($bookings as $booking) {
        $totalAmount += floatval($booking['amount'] ?? 0);
        $totalAdults += intval($booking['adult'] ?? 0);
        $totalChildren += intval($booking['child'] ?? 0);
        $totalInfants += intval($booking['infant'] ?? 0);
        $totalGuide += intval($booking['guide'] ?? 0);
        $totalFOC += intval($booking['inspection'] ?? 0);
        $totalTL += intval($booking['team_leader'] ?? 0);
    }

    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>All Floors Booking Report - ' . htmlspecialchars($date) . '</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 10mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 9px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .summary h3 {
            margin-top: 0;
            font-size: 14px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            margin-top: 5px;
        }
        .summary-item {
            text-align: center;
            padding: 8px;
            background-color: #e8e8e8;
            border-radius: 3px;
        }
        .summary-item strong {
            display: block;
            font-size: 13px;
            color: #007bff;
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sawasdee Booking Report - All Floors</h1>
        <p>Date: ' . htmlspecialchars($date) . '</p>
        <p>Exported on: ' . date('d-m-Y H:i:s') . '</p>
    </div>';

    if (empty($bookings)) {
        $html .= '<p style="text-align: center; padding: 20px;">No bookings found for this date.</p>';
    } else {
        $html .= '
        <table>
            <thead>
                <tr>
                    <th>Floor</th>
                    <th>Order No</th>
                    <th>Name</th>
                    <th>Phone</th>
                    <th>Tables</th>
                    <th>Adult</th>
                    <th>Child</th>
                    <th>Amount</th>
                    <th>Agent</th>
                    <th>Remark</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($bookings as $booking) {
            $floorName = '';
            switch ($booking['use_zone']) {
                case '1': $floorName = '1st Floor'; break;
                case '2': $floorName = '2nd Floor'; break;
                case '3': $floorName = '3rd Floor'; break;
                default: $floorName = 'Floor ' . $booking['use_zone']; break;
            }

            $html .= '
                <tr>
                    <td>' . htmlspecialchars($floorName) . '</td>
                    <td>' . htmlspecialchars($booking['orderNo'] ?? '') . '</td>
                    <td>' . htmlspecialchars($booking['name'] ?? '') . '</td>
                    <td>' . htmlspecialchars($booking['phone'] ?? '') . '</td>
                    <td>' . htmlspecialchars($booking['tables'] ?? '') . '</td>
                    <td>' . htmlspecialchars($booking['adult'] ?? '0') . '</td>
                    <td>' . htmlspecialchars($booking['child'] ?? '0') . '</td>
                    <td>฿' . number_format(floatval($booking['amount'] ?? 0), 2) . '</td>
                    <td>' . htmlspecialchars($booking['agent'] ?? '') . '</td>
                    <td>' . htmlspecialchars(substr($booking['remark'] ?? '', 0, 30)) . (strlen($booking['remark'] ?? '') > 30 ? '...' : '') . '</td>
                </tr>';
        }

        $html .= '
            </tbody>
        </table>';
    }

    // Add summary section
    $html .= '
    <div class="summary">
        <h3>Summary</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <strong>' . $totalBookings . '</strong>
                <span>Total Bookings</span>
            </div>
            <div class="summary-item">
                <strong>฿' . number_format($totalAmount, 2) . '</strong>
                <span>Total Amount</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalAdults . '</strong>
                <span>Total Adults</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalChildren . '</strong>
                <span>Total Children</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalInfants . '</strong>
                <span>Total Infants</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalGuide . '</strong>
                <span>Total Guide</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalFOC . '</strong>
                <span>Total FOC</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalTL . '</strong>
                <span>Total TL</span>
            </div>
        </div>
    </div>
</body>
</html>';

    return $html;
}

// Main execution
try {
    $date = $_GET['date'] ?? date('d-m-Y');
    $searchTerm = $_GET['search'] ?? '';

    generatePDF($date, $searchTerm);

} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>