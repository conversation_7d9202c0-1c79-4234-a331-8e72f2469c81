// Report functionality JavaScript

// Global variables for charts
let guest<PERSON><PERSON>, payment<PERSON>hart, floorChart;

// Generate report function
function generateReport() {
    const fromDate = document.getElementById('from-date').value;
    const toDate = document.getElementById('to-date').value;

    // Validate date range
    if (!fromDate || !toDate) {
        if (typeof Swal !== 'undefined') {
            Swal.fire('Error', 'Please select both from date and to date', 'error');
        } else {
            alert('Please select both from date and to date');
        }
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        if (typeof Swal !== 'undefined') {
            Swal.fire('Error', 'From date cannot be later than to date', 'error');
        } else {
            alert('From date cannot be later than to date');
        }
        return;
    }

    let params = {
        type: 'daterange',
        from_date: fromDate,
        to_date: toDate
    };

    // Show loading
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'Generating Report...',
            text: 'Please wait while we process your request',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    // Make API call
    const queryString = new URLSearchParams(params).toString();
    fetch(`api/generate_report.php?${queryString}`)
        .then(response => response.json())
        .then(data => {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
            if (data.success) {
                updateReportDisplay(data.data, 'daterange');
            } else {
                if (typeof Swal !== 'undefined') {
                    Swal.fire('Error', data.message || 'Failed to generate report', 'error');
                } else {
                    alert('Error: ' + (data.message || 'Failed to generate report'));
                }
            }
        })
        .catch(error => {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
            console.error('Error:', error);
            if (typeof Swal !== 'undefined') {
                Swal.fire('Error', 'Failed to generate report', 'error');
            } else {
                alert('Error: Failed to generate report');
            }
        });
}

// Update report display with new data
function updateReportDisplay(data, reportType) {
    // Update summary cards
    updateSummaryCards(data.summary);
    
    // Update detailed summary table
    updateDetailedSummary(data.summary);
    
    // Update new summary tables
    updateStatusSummaryTable(data.status_distribution || []);
    updateAgentSummaryTable(data.agent_summary || []);
    updateUserSummaryTable(data.user_summary || []);
    
    // Update charts
    updateCharts(data);
    
    // Update report title and date
    updateReportHeader(data, reportType);
}

// Update summary cards
function updateSummaryCards(summary) {
    const totalBookingsEl = document.getElementById('total-bookings');
    const totalAmountEl = document.getElementById('total-amount');
    const totalGuestsEl = document.getElementById('total-guests');
    const avgBookingValueEl = document.getElementById('avg-booking-value');

    if (totalBookingsEl) {
        totalBookingsEl.textContent = summary.total_bookings || 0;
    }

    if (totalAmountEl) {
        totalAmountEl.textContent = `฿${parseFloat(summary.total_amount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})}`;
    }

    if (totalGuestsEl) {
        const totalGuests = (parseInt(summary.total_adult || 0) + parseInt(summary.total_child || 0) + parseInt(summary.total_infant || 0));
        totalGuestsEl.textContent = totalGuests;
    }

    if (avgBookingValueEl) {
        // Calculate average booking value
        const avgBookingValue = summary.total_bookings > 0 ? (summary.total_amount / summary.total_bookings) : 0;
        avgBookingValueEl.textContent = `฿${avgBookingValue.toLocaleString('en-US', {minimumFractionDigits: 2})}`;
    }
}

// Update detailed summary table
function updateDetailedSummary(summary) {
    const totalGuests = parseInt(summary.total_adult || 0) + parseInt(summary.total_child || 0) + parseInt(summary.total_infant || 0);

    // Update counts
    const elements = [
        { id: 'total-adult', value: summary.total_adult || 0 },
        { id: 'total-child', value: summary.total_child || 0 },
        { id: 'total-infant', value: summary.total_infant || 0 },
        { id: 'total-guide', value: summary.total_guide || 0 },
        { id: 'total-foc', value: summary.total_foc || 0 },
        { id: 'total-tl', value: summary.total_tl || 0 }
    ];

    elements.forEach(item => {
        const el = document.getElementById(item.id);
        if (el) el.textContent = item.value;
    });

    // Calculate percentages
    const percentageElements = [
        { id: 'adult-percentage', value: summary.total_adult || 0 },
        { id: 'child-percentage', value: summary.total_child || 0 },
        { id: 'infant-percentage', value: summary.total_infant || 0 }
    ];

    percentageElements.forEach(item => {
        const el = document.getElementById(item.id);
        if (el) {
            if (totalGuests > 0) {
                el.textContent = `${((item.value / totalGuests) * 100).toFixed(1)}%`;
            } else {
                el.textContent = '0%';
            }
        }
    });
}

// Update status summary table
function updateStatusSummaryTable(statusData) {
    const tbody = document.querySelector('#status-summary-table tbody');
    tbody.innerHTML = '';
    
    if (statusData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">No data available</td></tr>';
        return;
    }
    
    statusData.forEach(status => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge ${getStatusBadgeClass(status.book_status)}">${status.book_status}</span></td>
            <td>${status.count}</td>
            <td>${status.total_people}</td>
            <td>฿${parseFloat(status.total_amount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})}</td>
        `;
        tbody.appendChild(row);
    });
}

// Update agent summary table
function updateAgentSummaryTable(agentData) {
    const tbody = document.querySelector('#agent-summary-table tbody');
    tbody.innerHTML = '';
    
    if (agentData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">No data available</td></tr>';
        return;
    }
    
    agentData.forEach(agent => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${agent.agent || 'N/A'}</td>
            <td>${agent.booking_count}</td>
            <td>${agent.total_people}</td>
            <td>฿${parseFloat(agent.total_amount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})}</td>
        `;
        tbody.appendChild(row);
    });
}

// Update user summary table
function updateUserSummaryTable(userData) {
    const tbody = document.querySelector('#user-summary-table tbody');
    tbody.innerHTML = '';

    if (userData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">No data available</td></tr>';
        return;
    }

    userData.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.user_key || 'N/A'}</td>
            <td>${user.booking_count}</td>
            <td>${user.total_people}</td>
            <td>฿${parseFloat(user.total_amount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})}</td>
        `;
        tbody.appendChild(row);
    });
}

// Get status badge class
function getStatusBadgeClass(status) {
    switch(status) {
        case 'Pending': return 'bg-info';
        case 'Complete': return 'bg-success';
        case 'Cancel': return 'bg-danger';
        case 'Change': return 'bg-warning';
        default: return 'bg-secondary';
    }
}

// Update charts
function updateCharts(data) {
    updateGuestChart(data.summary);
    updatePaymentChart(data.payment_distribution || []);
    updateFloorChart(data.floor_distribution || []);
}

// Update guest distribution chart
function updateGuestChart(summary) {
    const ctx = document.getElementById('guestChart').getContext('2d');
    
    if (guestChart) {
        guestChart.destroy();
    }
    
    guestChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Adults', 'Children', 'Infants', 'Guide', 'FOC', 'TL'],
            datasets: [{
                data: [
                    summary.total_adult || 0,
                    summary.total_child || 0,
                    summary.total_infant || 0,
                    summary.total_guide || 0,
                    summary.total_foc || 0,
                    summary.total_tl || 0
                ],
                backgroundColor: [
                    '#5D87FF',
                    '#49BEFF',
                    '#13DEB9',
                    '#FFAE1F',
                    '#FA896B',
                    '#539BFF'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Update payment status chart
function updatePaymentChart(paymentData) {
    const ctx = document.getElementById('paymentChart').getContext('2d');
    
    if (paymentChart) {
        paymentChart.destroy();
    }
    
    const labels = paymentData.map(item => item.payment_status || 'Unknown');
    const data = paymentData.map(item => item.count || 0);
    
    paymentChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#13DEB9',
                    '#FFAE1F',
                    '#FA896B',
                    '#5D87FF'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Update floor distribution chart
function updateFloorChart(floorData) {
    const ctx = document.getElementById('floorChart').getContext('2d');
    
    if (floorChart) {
        floorChart.destroy();
    }
    
    const labels = floorData.map(item => `Floor ${item.use_zone || 'Unknown'}`);
    const bookingCounts = floorData.map(item => item.count || 0);
    const amounts = floorData.map(item => item.amount || 0);
    
    floorChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Bookings',
                data: bookingCounts,
                backgroundColor: '#5D87FF',
                yAxisID: 'y'
            }, {
                label: 'Revenue (฿)',
                data: amounts,
                backgroundColor: '#13DEB9',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

// Update report header
function updateReportHeader(data, reportType) {
    const reportTitle = document.getElementById('report-title');
    const summaryDate = document.getElementById('summary-date');

    if (reportTitle) {
        reportTitle.textContent = 'Date Range Report';
    }

    if (summaryDate) {
        summaryDate.textContent = data.period_label || data.period || '';
    }
}

// Quick date range selection function
function setDateRange(range) {
    const today = new Date();
    const fromDateInput = document.getElementById('from-date');
    const toDateInput = document.getElementById('to-date');

    if (!fromDateInput || !toDateInput) {
        console.error('Date input elements not found');
        return;
    }

    let fromDate, toDate;

    switch(range) {
        case 'today':
            fromDate = toDate = today;
            break;
        case 'yesterday':
            fromDate = toDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            break;
        case 'week':
            // This week (Monday to Sunday)
            const dayOfWeek = today.getDay();
            const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
            fromDate = new Date(today.getTime() + mondayOffset * 24 * 60 * 60 * 1000);
            toDate = new Date(fromDate.getTime() + 6 * 24 * 60 * 60 * 1000);
            break;
        case 'lastweek':
            // Last week (Monday to Sunday)
            const lastWeekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const lastWeekDayOfWeek = lastWeekStart.getDay();
            const lastMondayOffset = lastWeekDayOfWeek === 0 ? -6 : 1 - lastWeekDayOfWeek;
            fromDate = new Date(lastWeekStart.getTime() + lastMondayOffset * 24 * 60 * 60 * 1000);
            toDate = new Date(fromDate.getTime() + 6 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            // This month
            fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
            toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'lastmonth':
            // Last month
            fromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            toDate = new Date(today.getFullYear(), today.getMonth(), 0);
            break;
        case 'quarter':
            // This quarter
            const quarter = Math.floor(today.getMonth() / 3);
            fromDate = new Date(today.getFullYear(), quarter * 3, 1);
            toDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
            break;
        case 'year':
            // This year
            fromDate = new Date(today.getFullYear(), 0, 1);
            toDate = new Date(today.getFullYear(), 11, 31);
            break;
        default:
            fromDate = toDate = today;
    }

    // Format dates as YYYY-MM-DD
    fromDateInput.value = fromDate.toISOString().split('T')[0];
    toDateInput.value = toDate.toISOString().split('T')[0];

    // Auto-generate report after setting dates
    setTimeout(() => {
        generateReport();
    }, 100);
}

// Export to Excel function
function exportToExcel() {
    const fromDate = document.getElementById('from-date').value;
    const toDate = document.getElementById('to-date').value;

    if (!fromDate || !toDate) {
        if (typeof Swal !== 'undefined') {
            Swal.fire('Error', 'Please generate a report first', 'error');
        } else {
            alert('Please generate a report first');
        }
        return;
    }

    // Create download URL
    const params = new URLSearchParams({
        type: 'daterange',
        from_date: fromDate,
        to_date: toDate,
        format: 'excel'
    });

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = `api/export_report.php?${params.toString()}`;
    link.download = `report_${fromDate}_to_${toDate}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Export to PDF function
function exportToPDF() {
    const fromDate = document.getElementById('from-date').value;
    const toDate = document.getElementById('to-date').value;

    if (!fromDate || !toDate) {
        if (typeof Swal !== 'undefined') {
            Swal.fire('Error', 'Please generate a report first', 'error');
        } else {
            alert('Please generate a report first');
        }
        return;
    }

    // Create download URL
    const params = new URLSearchParams({
        type: 'daterange',
        from_date: fromDate,
        to_date: toDate,
        format: 'pdf'
    });

    // Open PDF in new window
    window.open(`api/export_report.php?${params.toString()}`, '_blank');
}

// Initialize with today's date
document.addEventListener('DOMContentLoaded', function() {
    // Set default dates to today
    const today = new Date().toISOString().split('T')[0];
    const fromDateEl = document.getElementById('from-date');
    const toDateEl = document.getElementById('to-date');
    const generateBtn = document.getElementById('generate-report');
    const excelBtn = document.getElementById('export-report-excel');
    const pdfBtn = document.getElementById('export-report-pdf');

    if (fromDateEl) fromDateEl.value = today;
    if (toDateEl) toDateEl.value = today;

    // Add event listener to generate report button
    if (generateBtn) {
        generateBtn.addEventListener('click', generateReport);

        // Trigger initial report generation
        setTimeout(() => {
            generateReport();
        }, 500);
    }

    // Add event listeners to export buttons
    if (excelBtn) {
        excelBtn.addEventListener('click', exportToExcel);
    }

    if (pdfBtn) {
        pdfBtn.addEventListener('click', exportToPDF);
    }
});
