<?php
/**
 * Logout Process
 *
 * Handles user logout
 */

// Start session
session_start();

// Clean up user session tracking before destroying session
if (isset($_SESSION['id'])) {
    try {
        // Include database connection
        require_once '../dbconnect/_dbconnect.php';

        // Connect to database
        $conn = db_connect();

        if ($conn) {
            // Remove user's session from tracking table
            $sessionId = session_id();
            $userId = $_SESSION['id'];

            $deleteSQL = "DELETE FROM user_sessions WHERE user_id = ? AND session_id = ?";
            $stmt = $conn->prepare($deleteSQL);
            $stmt->execute([$userId, $sessionId]);

            // Close database connection
            db_close($conn);
        }
    } catch (Exception $e) {
        // Log error but don't fail logout
        error_log("Error cleaning up user session tracking: " . $e->getMessage());
    }
}

// Unset all session variables
$_SESSION = array();

// Destroy the session
session_destroy();

// Redirect to login page
header('Location: index.php');
exit;
?>
