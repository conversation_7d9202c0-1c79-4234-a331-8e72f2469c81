<?php
/**
 * Generate Report API
 * 
 * Generates various types of reports: daily, monthly, quarterly, year-to-date
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Include database connection
require_once '../../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $reportType = $input['type'] ?? 'daily';
    $startDate = $input['startDate'] ?? date('Y-m-d');
    $endDate = $input['endDate'] ?? date('Y-m-d');
    $month = $input['month'] ?? date('Y-m');
    $year = $input['year'] ?? date('Y');
    
    // Connect to database
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Generate report based on type
    switch ($reportType) {
        case 'daily':
            $reportData = generateDailyReport($conn, $startDate);
            break;
        case 'monthly':
            $reportData = generateMonthlyReport($conn, $month);
            break;
        case 'quarterly':
            $reportData = generateQuarterlyReport($conn, $startDate);
            break;
        case 'ytd':
            $reportData = generateYearToDateReport($conn, $year);
            break;
        default:
            throw new Exception('Invalid report type');
    }
    
    echo json_encode([
        'success' => true,
        'data' => $reportData,
        'type' => $reportType
    ]);
    
} catch (Exception $e) {
    error_log("Error in generate_report.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error generating report: ' . $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        db_close($conn);
    }
}

/**
 * Generate Daily Report
 */
function generateDailyReport($conn, $date) {
    // Get summary data (aggregated)
    $sql = "SELECT
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking
            WHERE use_date = ?
            AND book_status != 'Cancel'";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$date]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get floor distribution
    $floorSql = "SELECT use_zone, COUNT(*) as count, SUM(amount) as amount 
                 FROM kp_booking 
                 WHERE use_date = ? AND book_status != 'Cancel'
                 GROUP BY use_zone";
    $stmt = $conn->prepare($floorSql);
    $stmt->execute([$date]);
    $floorData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get payment status distribution
    $paymentSql = "SELECT payment_status, COUNT(*) as count 
                   FROM kp_booking 
                   WHERE use_date = ? AND book_status != 'Cancel'
                   GROUP BY payment_status";
    $stmt = $conn->prepare($paymentSql);
    $stmt->execute([$date]);
    $paymentData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get detailed bookings
    $detailSql = "SELECT * FROM kp_booking 
                  WHERE use_date = ? AND book_status != 'Cancel'
                  ORDER BY create_date DESC";
    $stmt = $conn->prepare($detailSql);
    $stmt->execute([$date]);
    $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'summary' => $summary ?: getEmptySummary(),
        'floor_distribution' => $floorData,
        'payment_distribution' => $paymentData,
        'bookings' => $bookings,
        'period' => $date,
        'period_label' => date('F j, Y', strtotime($date))
    ];
}

/**
 * Generate Monthly Report
 */
function generateMonthlyReport($conn, $month) {
    $year = substr($month, 0, 4);
    $monthNum = substr($month, 5, 2);
    
    $sql = "SELECT 
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking 
            WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
            AND book_status != 'Cancel'";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year, $monthNum]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get daily breakdown for the month
    $dailySql = "SELECT
                    DATE(use_date) as date,
                    COUNT(*) as bookings,
                    SUM(amount) as amount
                 FROM kp_booking
                 WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                 AND book_status != 'Cancel'
                 GROUP BY DATE(use_date)
                 ORDER BY DATE(use_date)";
    $stmt = $conn->prepare($dailySql);
    $stmt->execute([$year, $monthNum]);
    $dailyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get floor distribution
    $floorSql = "SELECT use_zone, COUNT(*) as count, SUM(amount) as amount 
                 FROM kp_booking 
                 WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                 AND book_status != 'Cancel'
                 GROUP BY use_zone";
    $stmt = $conn->prepare($floorSql);
    $stmt->execute([$year, $monthNum]);
    $floorData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'summary' => $summary ?: getEmptySummary(),
        'daily_breakdown' => $dailyData,
        'floor_distribution' => $floorData,
        'period' => $month,
        'period_label' => date('F Y', strtotime($month . '-01'))
    ];
}

/**
 * Generate Quarterly Report (3 months)
 */
function generateQuarterlyReport($conn, $startDate) {
    $endDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
    
    $sql = "SELECT 
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking 
            WHERE use_date >= ? AND use_date < ?
            AND book_status != 'Cancel'";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$startDate, $endDate]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get monthly breakdown
    $monthlySql = "SELECT 
                      YEAR(use_date) as year,
                      MONTH(use_date) as month,
                      COUNT(*) as bookings,
                      SUM(amount) as amount
                   FROM kp_booking 
                   WHERE use_date >= ? AND use_date < ?
                   AND book_status != 'Cancel'
                   GROUP BY YEAR(use_date), MONTH(use_date)
                   ORDER BY year, month";
    $stmt = $conn->prepare($monthlySql);
    $stmt->execute([$startDate, $endDate]);
    $monthlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'summary' => $summary ?: getEmptySummary(),
        'monthly_breakdown' => $monthlyData,
        'period' => $startDate . ' to ' . $endDate,
        'period_label' => date('M j, Y', strtotime($startDate)) . ' - ' . date('M j, Y', strtotime($endDate))
    ];
}

/**
 * Generate Year to Date Report
 */
function generateYearToDateReport($conn, $year) {
    $startDate = $year . '-01-01';
    $endDate = date('Y-m-d');
    
    $sql = "SELECT 
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking 
            WHERE YEAR(use_date) = ?
            AND book_status != 'Cancel'";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get monthly breakdown for the year
    $monthlySql = "SELECT 
                      MONTH(use_date) as month,
                      COUNT(*) as bookings,
                      SUM(amount) as amount
                   FROM kp_booking 
                   WHERE YEAR(use_date) = ?
                   AND book_status != 'Cancel'
                   GROUP BY MONTH(use_date)
                   ORDER BY month";
    $stmt = $conn->prepare($monthlySql);
    $stmt->execute([$year]);
    $monthlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'summary' => $summary ?: getEmptySummary(),
        'monthly_breakdown' => $monthlyData,
        'period' => $year,
        'period_label' => 'Year ' . $year . ' (Year to Date)'
    ];
}

/**
 * Get empty summary structure
 */
function getEmptySummary() {
    return [
        'total_bookings' => 0,
        'total_amount' => 0,
        'total_adult' => 0,
        'total_child' => 0,
        'total_infant' => 0,
        'total_guide' => 0,
        'total_foc' => 0,
        'total_tl' => 0
    ];
}
?>
