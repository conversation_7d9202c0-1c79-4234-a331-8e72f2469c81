<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once dirname(__DIR__) . '/config/config.php';

// Start session
session_start();

// Simple login process
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';

    // Validate input
    if (empty($username) || empty($password)) {
        $_SESSION['login_error'] = 'Please enter both username and password.';
        header('Location: index.php');
        exit;
    }

    try {
        // Connect to the database
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

        // Get user data
        $stmt = $pdo->prepare("SELECT * FROM kp_login WHERE user = ? AND status = 1");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if ($user) {
            // User found, verify password
            $authenticated = false;

            // Check if the password is hashed
            $password_info = password_get_info($user['pass']);

            // First try plain text comparison (for legacy passwords)
            if ($user['pass'] === $password) {
                $authenticated = true;

                // Upgrade to hashed password
                $hashedPassword = password_hash($password, PASSWORD_ARGON2ID, PASSWORD_OPTIONS);
                $updateStmt = $pdo->prepare("UPDATE kp_login SET pass = ?, update_date = NOW() WHERE id = ?");
                $updateStmt->execute([$hashedPassword, $user['id']]);
            }
            // If it looks like a hash, try password_verify
            else if ($password_info['algo'] !== 0) {
                $authenticated = password_verify($password, $user['pass']);

                // Check if the password hash needs to be rehashed
                if ($authenticated && password_needs_rehash($user['pass'], PASSWORD_ARGON2ID, PASSWORD_OPTIONS)) {
                    $hashedPassword = password_hash($password, PASSWORD_ARGON2ID, PASSWORD_OPTIONS);
                    $updateStmt = $pdo->prepare("UPDATE kp_login SET pass = ?, update_date = NOW() WHERE id = ?");
                    $updateStmt->execute([$hashedPassword, $user['id']]);
                }
            }

            if ($authenticated) {
                // Set session variables
                $_SESSION['loggedin'] = true;
                $_SESSION['username'] = $user['user'];
                $_SESSION['role'] = $user['role'];   // super_admin, admin, user
                $_SESSION['id'] = $user['id'];
                $_SESSION['name'] = $user['name'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['userKey'] = $user['user_key'];
                $_SESSION['userStatus'] = $user['status'];
                $_SESSION['first_login'] = $user['first_login']; // Track if user needs to change password

                // Set last activity time for session timeout
                $_SESSION['last_activity'] = time();

                // Initialize user session tracking
                try {
                    $sessionId = session_id();
                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

                    // Create user_sessions table if it doesn't exist
                    $createTableSQL = "
                        CREATE TABLE IF NOT EXISTS user_sessions (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            user_id INT NOT NULL,
                            session_id VARCHAR(255) NOT NULL,
                            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            ip_address VARCHAR(45),
                            user_agent TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_user_id (user_id),
                            INDEX idx_session_id (session_id),
                            INDEX idx_last_activity (last_activity),
                            FOREIGN KEY (user_id) REFERENCES kp_login(id) ON DELETE CASCADE
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    ";

                    $pdo->exec($createTableSQL);

                    // Insert user session
                    $insertSessionSQL = "
                        INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent, last_activity)
                        VALUES (?, ?, ?, ?, NOW())
                        ON DUPLICATE KEY UPDATE
                        last_activity = NOW(),
                        ip_address = VALUES(ip_address),
                        user_agent = VALUES(user_agent)
                    ";

                    $sessionStmt = $pdo->prepare($insertSessionSQL);
                    $sessionStmt->execute([$user['id'], $sessionId, $ipAddress, $userAgent]);
                } catch (Exception $e) {
                    // Log error but don't fail login
                    error_log("Error initializing user session tracking: " . $e->getMessage());
                }

                // Log successful login
                error_log("Successful login: User {$username} from IP {$_SERVER['REMOTE_ADDR']}");

                // Check if user needs to change password on first login
                if ($user['first_login'] == 1) {
                    header('Location: change_password.php');
                    exit;
                }

                // Redirect to dashboard
                header('Location: ../floorplan/');
                exit;
            } else {
                // Invalid password
                $_SESSION['login_error'] = 'Invalid username or password.';
                header('Location: index.php');
                exit;
            }
        } else {
            // User not found or account is disabled
            $_SESSION['login_error'] = 'Invalid username or password.';
            header('Location: index.php');
            exit;
        }
    } catch (PDOException $e) {
        // Log the error but don't expose details to the user
        error_log("Database error: " . $e->getMessage());
        $_SESSION['login_error'] = 'An error occurred while processing your request. Please try again later.';
        header('Location: index.php');
        exit;
    }
} else {
    // Not a POST request
    $_SESSION['login_error'] = 'Invalid access method. Please use the login form.';
    header('Location: index.php');
    exit;
}
