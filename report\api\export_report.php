<?php
/**
 * Export Report API
 * 
 * Exports reports in Excel or PDF format
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo 'Unauthorized';
    exit;
}

// Include database connection
require_once '../../dbconnect/_dbconnect.php';

// Accept both GET and POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo 'Method not allowed';
    exit;
}

try {
    // Get input data from both GET and POST
    $input = json_decode(file_get_contents('php://input'), true);

    // Support both GET and POST parameters
    $reportType = $input['type'] ?? $_GET['type'] ?? 'daterange';
    $startDate = $input['from_date'] ?? $_GET['from_date'] ?? date('Y-m-d');
    $endDate = $input['to_date'] ?? $_GET['to_date'] ?? date('Y-m-d');
    $format = $input['format'] ?? $_GET['format'] ?? 'excel';

    // Connect to database
    $conn = db_connect();

    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    // Get report data
    $reportData = generateDateRangeReport($conn, $startDate, $endDate);

    if ($format === 'excel') {
        exportToExcel($reportData, $startDate, $endDate);
    } elseif ($format === 'pdf') {
        exportToPDF($reportData, $startDate, $endDate);
    } else {
        throw new Exception('Invalid export format');
    }

} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo 'Export failed: ' . $e->getMessage();
}

/**
 * Generate date range report data
 */
function generateDateRangeReport($conn, $startDate, $endDate) {
    // Get summary data
    $summarySql = "SELECT 
                    COUNT(*) as total_bookings,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult), 0) as total_adult,
                    COALESCE(SUM(child), 0) as total_child,
                    COALESCE(SUM(infant), 0) as total_infant,
                    COALESCE(SUM(guide), 0) as total_guide,
                    COALESCE(SUM(inspection), 0) as total_foc,
                    COALESCE(SUM(team_leader), 0) as total_tl
                FROM kp_booking 
                WHERE use_date >= ? AND use_date <= ?
                AND book_status != 'Cancel'";
    $stmt = $conn->prepare($summarySql);
    $stmt->execute([$startDate, $endDate]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get detailed booking data
    $detailSql = "SELECT 
                    orderNo,
                    name,
                    phone,
                    adult,
                    child,
                    infant,
                    guide,
                    inspection as foc,
                    team_leader as tl,
                    use_date,
                    use_zone as floor,
                    agent,
                    amount,
                    payment_status,
                    payment_type,
                    book_status,
                    remark
                FROM kp_booking 
                WHERE use_date >= ? AND use_date <= ?
                ORDER BY use_date DESC, orderNo";
    $stmt = $conn->prepare($detailSql);
    $stmt->execute([$startDate, $endDate]);
    $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent summary
    $agentSql = "SELECT 
                    agent,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                FROM kp_booking 
                WHERE use_date >= ? AND use_date <= ?
                AND book_status != 'Cancel'
                AND agent IS NOT NULL AND agent != ''
                GROUP BY agent
                ORDER BY booking_count DESC";
    $stmt = $conn->prepare($agentSql);
    $stmt->execute([$startDate, $endDate]);
    $agentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'summary' => $summary,
        'bookings' => $bookings,
        'agents' => $agentData,
        'period' => [
            'start' => $startDate,
            'end' => $endDate
        ]
    ];
}

/**
 * Export to Excel format
 */
function exportToExcel($data, $startDate, $endDate) {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="report_' . $startDate . '_to_' . $endDate . '.xls"');
    header('Cache-Control: max-age=0');

    // Start output
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel">';
    echo '<head><meta charset="UTF-8"></head>';
    echo '<body>';
    
    // Report title
    echo '<h2>Booking Report: ' . date('d/m/Y', strtotime($startDate)) . ' to ' . date('d/m/Y', strtotime($endDate)) . '</h2>';
    
    // Summary section
    echo '<h3>Summary</h3>';
    echo '<table border="1">';
    echo '<tr><th>Metric</th><th>Value</th></tr>';
    echo '<tr><td>Total Bookings</td><td>' . $data['summary']['total_bookings'] . '</td></tr>';
    echo '<tr><td>Total Amount</td><td>฿' . number_format($data['summary']['total_amount'], 2) . '</td></tr>';
    echo '<tr><td>Total Adults</td><td>' . $data['summary']['total_adult'] . '</td></tr>';
    echo '<tr><td>Total Children</td><td>' . $data['summary']['total_child'] . '</td></tr>';
    echo '<tr><td>Total Infants</td><td>' . $data['summary']['total_infant'] . '</td></tr>';
    echo '<tr><td>Total Guides</td><td>' . $data['summary']['total_guide'] . '</td></tr>';
    echo '<tr><td>Total FOC</td><td>' . $data['summary']['total_foc'] . '</td></tr>';
    echo '<tr><td>Total TL</td><td>' . $data['summary']['total_tl'] . '</td></tr>';
    echo '</table><br>';
    
    // Detailed bookings
    echo '<h3>Detailed Bookings</h3>';
    echo '<table border="1">';
    echo '<tr>';
    echo '<th>Order No</th><th>Name</th><th>Phone</th><th>Adults</th><th>Children</th><th>Infants</th>';
    echo '<th>Guides</th><th>FOC</th><th>TL</th><th>Use Date</th><th>Floor</th><th>Agent</th>';
    echo '<th>Amount</th><th>Payment Status</th><th>Payment Type</th><th>Status</th><th>Remark</th>';
    echo '</tr>';
    
    foreach ($data['bookings'] as $booking) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($booking['orderNo']) . '</td>';
        echo '<td>' . htmlspecialchars($booking['name']) . '</td>';
        echo '<td>' . htmlspecialchars($booking['phone']) . '</td>';
        echo '<td>' . $booking['adult'] . '</td>';
        echo '<td>' . $booking['child'] . '</td>';
        echo '<td>' . $booking['infant'] . '</td>';
        echo '<td>' . $booking['guide'] . '</td>';
        echo '<td>' . $booking['foc'] . '</td>';
        echo '<td>' . $booking['tl'] . '</td>';
        echo '<td>' . date('d/m/Y', strtotime($booking['use_date'])) . '</td>';
        echo '<td>' . htmlspecialchars($booking['floor']) . '</td>';
        echo '<td>' . htmlspecialchars($booking['agent']) . '</td>';
        echo '<td>฿' . number_format($booking['amount'], 2) . '</td>';
        echo '<td>' . htmlspecialchars($booking['payment_status']) . '</td>';
        echo '<td>' . htmlspecialchars($booking['payment_type']) . '</td>';
        echo '<td>' . htmlspecialchars($booking['book_status']) . '</td>';
        echo '<td>' . htmlspecialchars($booking['remark']) . '</td>';
        echo '</tr>';
    }
    echo '</table><br>';
    
    // Agent summary
    if (!empty($data['agents'])) {
        echo '<h3>Agent Summary</h3>';
        echo '<table border="1">';
        echo '<tr><th>Agent</th><th>Bookings</th><th>Total People</th><th>Total Amount</th></tr>';
        
        foreach ($data['agents'] as $agent) {
            echo '<tr>';
            echo '<td>' . htmlspecialchars($agent['agent']) . '</td>';
            echo '<td>' . $agent['booking_count'] . '</td>';
            echo '<td>' . $agent['total_people'] . '</td>';
            echo '<td>฿' . number_format($agent['total_amount'], 2) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
    
    echo '</body></html>';
}

/**
 * Export to PDF format
 */
function exportToPDF($data, $startDate, $endDate) {
    // For now, we'll create a simple HTML page that can be printed as PDF
    // In the future, you can integrate a proper PDF library like TCPDF or mPDF
    
    header('Content-Type: text/html; charset=UTF-8');
    
    echo '<!DOCTYPE html>';
    echo '<html><head>';
    echo '<meta charset="UTF-8">';
    echo '<title>Booking Report</title>';
    echo '<style>';
    echo 'body { font-family: Arial, sans-serif; margin: 20px; }';
    echo 'table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }';
    echo 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
    echo 'th { background-color: #f2f2f2; }';
    echo 'h2, h3 { color: #333; }';
    echo '@media print { body { margin: 0; } }';
    echo '</style>';
    echo '</head><body>';
    
    // Report title
    echo '<h2>Booking Report: ' . date('d/m/Y', strtotime($startDate)) . ' to ' . date('d/m/Y', strtotime($endDate)) . '</h2>';
    
    // Summary section
    echo '<h3>Summary</h3>';
    echo '<table>';
    echo '<tr><th>Metric</th><th>Value</th></tr>';
    echo '<tr><td>Total Bookings</td><td>' . $data['summary']['total_bookings'] . '</td></tr>';
    echo '<tr><td>Total Amount</td><td>฿' . number_format($data['summary']['total_amount'], 2) . '</td></tr>';
    echo '<tr><td>Total Adults</td><td>' . $data['summary']['total_adult'] . '</td></tr>';
    echo '<tr><td>Total Children</td><td>' . $data['summary']['total_child'] . '</td></tr>';
    echo '<tr><td>Total Infants</td><td>' . $data['summary']['total_infant'] . '</td></tr>';
    echo '<tr><td>Total Guides</td><td>' . $data['summary']['total_guide'] . '</td></tr>';
    echo '<tr><td>Total FOC</td><td>' . $data['summary']['total_foc'] . '</td></tr>';
    echo '<tr><td>Total TL</td><td>' . $data['summary']['total_tl'] . '</td></tr>';
    echo '</table>';
    
    // Add print button and instructions
    echo '<div style="margin: 20px 0; padding: 10px; background-color: #f0f0f0; border-radius: 5px;">';
    echo '<p><strong>Instructions:</strong> Use your browser\'s print function (Ctrl+P) to save as PDF or print this report.</p>';
    echo '<button onclick="window.print()" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Print Report</button>';
    echo '</div>';
    
    echo '</body></html>';
}
?>
